<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Giftcode;
use App\Models\GiftcodeLog;
use App\Services\GiftcodeService;

echo "=== Test Hệ thống Giftcode ===\n\n";

try {
    // Test 1: Tạo giftcode đơn
    echo "1. Test tạo giftcode đơn...\n";
    
    $giftcodeService = app(App\Services\GiftcodeService::class);
    
    $data = [
        'type' => 1, // Public
        'accounts' => null,
        'multiple' => false,
        'code' => 'TEST2025',
        'items' => ['14,5,1,0,0,0,0', '15,10,1,0,0,0,0'],
        'content' => 'Test giftcode đơn',
        'limit' => 100,
        'period' => 30,
        'zoneid' => 1
    ];

    $result = $giftcodeService->createGiftcode($data);
    
    if ($result['success']) {
        echo "✓ Tạo giftcode thành công!\n";
        echo "  - ID: " . $result['giftcode']->id . "\n";
        echo "  - Codes: " . implode(', ', $result['codes']) . "\n";
    } else {
        echo "✗ Lỗi: " . $result['message'] . "\n";
    }
    
    echo "\n";

    // Test 2: Tạo nhiều giftcode
    echo "2. Test tạo nhiều giftcode...\n";
    
    $data2 = [
        'type' => 1,
        'accounts' => null,
        'multiple' => true,
        'number' => 3,
        'items' => ['14,5,1,0,0,0,0'],
        'content' => 'Test nhiều giftcode',
        'limit' => 50,
        'period' => 0,
        'zoneid' => 0
    ];

    $result2 = $giftcodeService->createGiftcode($data2);
    
    if ($result2['success']) {
        echo "✓ Tạo nhiều giftcode thành công!\n";
        echo "  - ID: " . $result2['giftcode']->id . "\n";
        echo "  - Codes: " . implode(', ', $result2['codes']) . "\n";
    } else {
        echo "✗ Lỗi: " . $result2['message'] . "\n";
    }
    
    echo "\n";

    // Test 3: Kiểm tra validation
    echo "3. Test validation giftcode...\n";
    
    $giftcode = Giftcode::create([
        'type' => 1,
        'accounts' => null,
        'multiple' => false,
        'code' => ['TESTVALID'],
        'items' => ['14,5,1,0,0,0,0'],
        'content' => 'Test validation',
        'limit' => 1,
        'period' => 30,
        'zoneid' => 1
    ]);

    // Test valid usage
    if ($giftcode->canBeUsedByUser(1, 1)) {
        echo "✓ Giftcode có thể sử dụng\n";
    } else {
        echo "✗ Giftcode không thể sử dụng\n";
    }

    // Create usage log
    GiftcodeLog::createLog(1, 1, 1, 'TESTVALID', $giftcode->id);

    // Test that it can't be used again
    if (!$giftcode->canBeUsedByUser(1, 1)) {
        echo "✓ Giftcode không thể sử dụng lại\n";
    } else {
        echo "✗ Giftcode vẫn có thể sử dụng lại (lỗi)\n";
    }
    
    echo "\n";

    // Test 4: Kiểm tra hết hạn
    echo "4. Test kiểm tra hết hạn...\n";
    
    $expiredGiftcode = Giftcode::create([
        'type' => 1,
        'accounts' => null,
        'multiple' => false,
        'code' => ['EXPIRED'],
        'items' => ['14,5,1,0,0,0,0'],
        'content' => 'Expired test',
        'limit' => 100,
        'period' => 1, // 1 day
        'zoneid' => 1,
        'created_at' => now()->subDays(2) // Created 2 days ago
    ]);

    if ($expiredGiftcode->isExpired()) {
        echo "✓ Giftcode đã hết hạn được phát hiện đúng\n";
    } else {
        echo "✗ Giftcode hết hạn không được phát hiện\n";
    }
    
    echo "\n";

    // Test 5: Kiểm tra private giftcode
    echo "5. Test private giftcode...\n";
    
    $privateGiftcode = Giftcode::create([
        'type' => 2, // Private
        'accounts' => 'testuser1,testuser2',
        'multiple' => false,
        'code' => ['PRIVATE'],
        'items' => ['14,5,1,0,0,0,0'],
        'content' => 'Private test',
        'limit' => 100,
        'period' => 0,
        'zoneid' => 1
    ]);

    $allowedAccounts = $privateGiftcode->getAllowedAccounts();
    if ($allowedAccounts === ['testuser1', 'testuser2']) {
        echo "✓ Private giftcode accounts được parse đúng\n";
    } else {
        echo "✗ Private giftcode accounts không đúng\n";
        echo "  Expected: ['testuser1', 'testuser2']\n";
        echo "  Got: " . json_encode($allowedAccounts) . "\n";
    }
    
    echo "\n";

    // Test 6: Hiển thị thống kê
    echo "6. Thống kê hệ thống...\n";
    
    $totalGiftcodes = Giftcode::count();
    $totalLogs = GiftcodeLog::count();
    
    echo "✓ Tổng số giftcode: $totalGiftcodes\n";
    echo "✓ Tổng số lần sử dụng: $totalLogs\n";
    
    echo "\n=== Test hoàn thành ===\n";

} catch (Exception $e) {
    echo "✗ Lỗi: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
