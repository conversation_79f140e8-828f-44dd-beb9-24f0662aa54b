<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use App\Models\Giftcode;
use App\Models\GiftcodeLog;
use App\Services\ItemService;

class GiftcodeController extends Controller
{
    public function index(Request $request)
    {
        $admin = Session::get('admin_user');
        $search = $request->get('search');
        $statusFilter = $request->get('status', 'all');
        $typeFilter = $request->get('type', 'all');

        // Base query for giftcodes using new model
        $query = Giftcode::with(['logs'])
            ->leftJoin('admin_users as a', 'giftcodes.created_by', '=', 'a.id')
            ->select([
                'giftcodes.*',
                'a.username as admin_username'
            ]);

        // Apply search filters
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('giftcodes.code', 'like', "%{$search}%")
                    ->orWhere('giftcodes.name', 'like', "%{$search}%")
                    ->orWhere('giftcodes.description', 'like', "%{$search}%")
                    ->orWhere('giftcodes.content', 'like', "%{$search}%");
            });
        }

        // Apply status filter
        if ($statusFilter !== 'all') {
            switch ($statusFilter) {
                case 'active':
                    $query->where('giftcodes.is_active', true)
                        ->where(function ($q) {
                            $q->where('giftcodes.period', 0)
                                ->orWhereRaw('DATE_ADD(giftcodes.created_at, INTERVAL giftcodes.period DAY) > NOW()');
                        });
                    break;
                case 'expired':
                    $query->where('giftcodes.period', '>', 0)
                        ->whereRaw('DATE_ADD(giftcodes.created_at, INTERVAL giftcodes.period DAY) <= NOW()');
                    break;
                case 'inactive':
                    $query->where('giftcodes.is_active', false);
                    break;
                case 'used_up':
                    $query->where('giftcodes.limit', '>', 0)
                        ->whereRaw('(SELECT COUNT(*) FROM giftcode_logs WHERE groupid = giftcodes.id) >= giftcodes.limit');
                    break;
            }
        }

        $giftcodes = $query->orderBy('giftcodes.created_at', 'desc')->paginate(20);

        // Add usage count for each giftcode
        foreach ($giftcodes as $giftcode) {
            $giftcode->usage_count = GiftcodeLog::where('groupid', $giftcode->id)->count();
            $giftcode->remaining_uses = $giftcode->limit > 0 ? max(0, $giftcode->limit - $giftcode->usage_count) : 'Không giới hạn';
        }

        // Get statistics
        $stats = [
            'total_giftcodes' => Giftcode::count(),
            'active_giftcodes' => Giftcode::where('is_active', true)
                ->where(function ($q) {
                    $q->where('period', 0)
                        ->orWhereRaw('DATE_ADD(created_at, INTERVAL period DAY) > NOW()');
                })
                ->count(),
            'expired_giftcodes' => Giftcode::where('period', '>', 0)
                ->whereRaw('DATE_ADD(created_at, INTERVAL period DAY) <= NOW()')
                ->count(),
            'total_usage' => GiftcodeLog::count()
        ];

        // Rename stats keys to match view
        $stats = [
            'total_codes' => $stats['total_giftcodes'],
            'active_codes' => $stats['active_giftcodes'],
            'expired_codes' => $stats['expired_giftcodes'],
            'total_usage' => $stats['total_usage']
        ];

        return view('admin.giftcode.index', compact('giftcodes', 'search', 'statusFilter', 'typeFilter', 'stats'));
    }

    public function create()
    {
        $admin = Session::get('admin_user');
        $itemList = ItemService::getItemsByCategory();
        $popularItems = ItemService::getPopularItems();
        return view('admin.giftcode.create', compact('admin', 'itemList', 'popularItems'));
    }

    /**
     * API endpoint to get item list
     */
    public function getItems(Request $request)
    {
        $search = $request->get('search');
        $category = $request->get('category');

        if ($search) {
            $items = ItemService::searchItems($search);
        } elseif ($category) {
            $itemsByCategory = ItemService::getItemsByCategory();
            $items = $itemsByCategory[$category] ?? [];
        } else {
            $items = ItemService::getItemList();
        }

        return response()->json([
            'success' => true,
            'items' => $items,
            'categories' => array_keys(ItemService::getItemsByCategory())
        ]);
    }

    public function store(Request $request)
    {
        $admin = Session::get('admin_user');

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'code_type' => 'required|in:single,multiple',
            'code' => 'required_if:code_type,single|string|max:50|unique:giftcodes,code',
            'code_prefix' => 'required_if:code_type,multiple|string|max:20|regex:/^[A-Z0-9_]+$/',
            'code_count' => 'required_if:code_type,multiple|integer|min:1|max:1000',
            'max_uses' => 'required|integer|min:1|max:10000',
            'expires_at' => 'nullable|date|after:now',
            'reward_type' => 'required|in:coins,items,mixed',
            'reward_coins' => 'required_if:reward_type,coins,mixed|integer|min:0',
            'reward_items' => 'required_if:reward_type,items,mixed|string',
        ], [
            'code_prefix.required_if' => 'Tiền tố code là bắt buộc khi tạo nhiều code.',
            'code_prefix.string' => 'Tiền tố code phải là chuỗi ký tự.',
            'code_prefix.regex' => 'Tiền tố code chỉ được chứa chữ cái, số và dấu gạch dưới.',
            'code.required_if' => 'Mã giftcode là bắt buộc khi tạo code đơn lẻ.',
            'code.unique' => 'Mã giftcode này đã tồn tại.',
            'code_count.required_if' => 'Số lượng code là bắt buộc khi tạo nhiều code.',
            'reward_coins.required_if' => 'Số coin thưởng là bắt buộc.',
            'reward_items.required_if' => 'Danh sách item thưởng là bắt buộc.',
        ]);

        // Generate codes
        $codes = [];
        if ($request->code_type === 'single') {
            $codes[] = strtoupper($request->code);
        } else {
            $prefix = strtoupper($request->code_prefix);
            for ($i = 0; $i < $request->code_count; $i++) {
                // Generate more random and secure codes
                $codes[] = $prefix . '_' . substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 8);
            }
        }

        // Prepare rewards
        $rewards = [];
        if ($request->reward_type === 'coins' || $request->reward_type === 'mixed') {
            $rewards['coins'] = (int) $request->reward_coins;
        }
        if ($request->reward_type === 'items' || $request->reward_type === 'mixed') {
            $items = [];
            $itemLines = explode("\n", trim($request->reward_items));
            foreach ($itemLines as $line) {
                $line = trim($line);
                if (!empty($line)) {
                    $parts = explode(',', $line);
                    if (count($parts) >= 2) {
                        $items[] = [
                            'id' => trim($parts[0]),
                            'quantity' => intval(trim($parts[1])),
                            'name' => isset($parts[2]) ? trim($parts[2]) : 'Item'
                        ];
                    }
                }
            }
            $rewards['items'] = $items;
        }



        // Create giftcodes
        foreach ($codes as $code) {
            Giftcode::create([
                'code' => $code,
                'name' => $request->name,
                'description' => $request->description,
                'rewards' => json_encode($rewards),
                'max_uses' => $request->max_uses,
                'used_count' => 0,
                'is_active' => true,
                'expires_at' => $request->expires_at,
                'created_by' => $admin['id']
            ]);
        }

        // Log admin action
        $this->logAdminAction(
            $admin,
            'create_giftcode',
            'giftcode',
            $codes[0], // Use the first code as the target identifier
            $request->name,
            [], // No old data
            [
                'code_count' => count($codes),
                'codes' => $codes,
                'rewards' => $rewards,
                'max_uses' => $request->max_uses,
                'expires_at' => $request->expires_at,
            ],
            "Tạo " . count($codes) . " giftcode: " . $request->name,
            $request->ip()
        );

        return redirect()->route('admin.giftcode.index')
            ->with('success', "Đã tạo thành công " . count($codes) . " giftcode!");
    }

    public function show($id)
    {
        $admin = Session::get('admin_user');

        $giftcode = DB::table('giftcodes as g')
            ->leftJoin('admin_users as a', 'g.created_by', '=', 'a.id')
            ->select([
                'g.*',
                'a.username as admin_username'
            ])
            ->where('g.id', $id)
            ->first();

        if (!$giftcode) {
            return redirect()->route('admin.giftcode.index')->withErrors(['error' => 'Không tìm thấy giftcode.']);
        }

        // Get usage history
        $usageHistory = DB::table('giftcode_usage')
            ->where('giftcode_id', $id)
            ->orderBy('used_at', 'desc')
            ->limit(50)
            ->get();

        // Decode rewards
        $giftcode->rewards = json_decode($giftcode->rewards, true);

        return view('admin.giftcode.show', compact('admin', 'giftcode', 'usageHistory'));
    }

    public function edit($id)
    {
        $admin = Session::get('admin_user');

        $giftcode = DB::table('giftcodes')->where('id', $id)->first();

        if (!$giftcode) {
            return redirect()->route('admin.giftcode.index')->withErrors(['error' => 'Không tìm thấy giftcode.']);
        }

        // Decode rewards
        $giftcode->rewards = json_decode($giftcode->rewards, true);

        return view('admin.giftcode.edit', compact('admin', 'giftcode'));
    }

    public function update(Request $request, $id)
    {
        $admin = Session::get('admin_user');

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'max_uses' => 'required|integer|min:1|max:10000',
            'expires_at' => 'nullable|date',
            'is_active' => 'boolean',
        ]);

        // Get giftcode info before update
        $giftcode = DB::table('giftcodes')->where('id', $id)->first();
        if (!$giftcode) {
            return redirect()->route('admin.giftcode.index')->withErrors(['error' => 'Không tìm thấy giftcode.']);
        }

        $oldData = [
            'name' => $giftcode->name,
            'max_uses' => $giftcode->max_uses,
            'is_active' => $giftcode->is_active,
            'expires_at' => $giftcode->expires_at,
        ];

        $newData = [
            'name' => $request->name,
            'max_uses' => $request->max_uses,
            'is_active' => $request->has('is_active'),
            'expires_at' => $request->expires_at,
        ];

        // Update giftcode
        DB::table('giftcodes')
            ->where('id', $id)
            ->update([
                'name' => $request->name,
                'description' => $request->description,
                'max_uses' => $request->max_uses,
                'is_active' => $request->has('is_active'),
                'expires_at' => $request->expires_at,
                'updated_at' => now()
            ]);

        // Log admin action
        $this->logAdminAction(
            $admin,
            'edit_giftcode',
            'giftcode',
            $id,
            $giftcode->code,
            $oldData,
            $newData,
            'Cập nhật thông tin giftcode',
            $request->ip()
        );

        return redirect()->route('admin.giftcode.show', $id)
            ->with('success', "Đã cập nhật giftcode thành công.");
    }

    public function destroy($id)
    {
        $admin = Session::get('admin_user');

        // Get giftcode info before delete
        $giftcode = DB::table('giftcodes')->where('id', $id)->first();
        if (!$giftcode) {
            return redirect()->route('admin.giftcode.index')->withErrors(['error' => 'Không tìm thấy giftcode.']);
        }

        // Delete giftcode
        DB::table('giftcodes')->where('id', $id)->delete();

        // Log admin action
        $this->logAdminAction(
            $admin,
            'delete_giftcode',
            'giftcode',
            $id,
            $giftcode->code,
            ['code' => $giftcode->code, 'name' => $giftcode->name],
            [],
            'Xóa giftcode',
            request()->ip()
        );

        return redirect()->route('admin.giftcode.index')
            ->with('success', "Đã xóa giftcode thành công.");
    }

    public function toggleStatus($id)
    {
        $admin = Session::get('admin_user');

        $giftcode = DB::table('giftcodes')->where('id', $id)->first();
        if (!$giftcode) {
            return response()->json(['success' => false, 'message' => 'Không tìm thấy giftcode']);
        }

        $newStatus = !$giftcode->is_active;

        DB::table('giftcodes')
            ->where('id', $id)
            ->update([
                'is_active' => $newStatus,
                'updated_at' => now()
            ]);

        // Log admin action
        $this->logAdminAction(
            $admin,
            'toggle_giftcode_status',
            'giftcode',
            $id,
            $giftcode->code,
            ['is_active' => $giftcode->is_active],
            ['is_active' => $newStatus],
            $newStatus ? 'Kích hoạt giftcode' : 'Vô hiệu hóa giftcode',
            request()->ip()
        );

        return response()->json([
            'success' => true,
            'message' => $newStatus ? 'Đã kích hoạt giftcode' : 'Đã vô hiệu hóa giftcode',
            'is_active' => $newStatus
        ]);
    }

    public function usageReport(Request $request)
    {
        $admin = Session::get('admin_user');

        // Get date range from request or default to last 30 days
        $startDate = $request->get('start_date', now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->format('Y-m-d'));

        // Get giftcode usage statistics
        $usageStats = DB::table('giftcode_logs as gl')
            ->join('giftcodes as g', 'gl.groupid', '=', 'g.id')
            ->select([
                'g.id',
                'g.code',
                'g.name',
                'g.limit',
                DB::raw('COUNT(gl.id) as total_usage'),
                DB::raw('DATE(gl.created_at) as usage_date')
            ])
            ->whereBetween('gl.created_at', [$startDate, $endDate])
            ->groupBy('g.id', 'g.code', 'g.name', 'g.limit', 'usage_date')
            ->orderBy('usage_date', 'desc')
            ->orderBy('total_usage', 'desc')
            ->get();

        // Get top used giftcodes
        $topGiftcodes = DB::table('giftcode_logs as gl')
            ->join('giftcodes as g', 'gl.groupid', '=', 'g.id')
            ->select([
                'g.id',
                'g.code',
                'g.name',
                'g.limit',
                DB::raw('COUNT(gl.id) as total_usage')
            ])
            ->whereBetween('gl.created_at', [$startDate, $endDate])
            ->groupBy('g.id', 'g.code', 'g.name', 'g.limit')
            ->orderBy('total_usage', 'desc')
            ->limit(10)
            ->get();

        // Get daily usage summary
        $dailyUsage = DB::table('giftcode_logs')
            ->select([
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as total_usage')
            ])
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date', 'desc')
            ->get();

        return view('admin.giftcode.usage-report', compact(
            'admin',
            'usageStats',
            'topGiftcodes',
            'dailyUsage',
            'startDate',
            'endDate'
        ));
    }

    private function logAdminAction($admin, $action, $targetType, $targetId, $targetName, $oldData, $newData, $reason, $ip)
    {
        DB::table('admin_action_logs')->insert([
            'admin_id' => $admin['id'],
            'admin_username' => $admin['username'],
            'action' => $action,
            'target_type' => $targetType,
            'target_id' => $targetId,
            'target_name' => $targetName,
            'old_data' => json_encode($oldData),
            'new_data' => json_encode($newData),
            'reason' => $reason,
            'ip_address' => $ip,
            'user_agent' => request()->header('User-Agent'),
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }
}
