<?php

namespace App\Services;

use App\Models\Giftcode;
use App\Models\GiftcodeLog;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Exception;

class GiftcodeService
{
    /**
     * Use a giftcode for a user and character
     */
    public function useGiftcode($giftcodeString, $userId, $characterId, $zoneId = 1)
    {
        try {
            // Normalize the giftcode
            $giftcodeString = strtoupper(trim($giftcodeString));
            
            // Find the giftcode
            $giftcode = $this->findGiftcode($giftcodeString);
            if (!$giftcode) {
                return [
                    'success' => false,
                    'message' => 'Mã giftcode không tồn tại hoặc không hợp lệ!'
                ];
            }

            // Validate giftcode
            $validation = $this->validateGiftcode($giftcode, $userId, $characterId, $zoneId);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'message' => $validation['message']
                ];
            }

            // Get user and character info
            $userInfo = $this->getUserInfo($userId);
            $characterInfo = $this->getCharacterInfo($characterId, $zoneId);
            
            if (!$userInfo || !$characterInfo) {
                return [
                    'success' => false,
                    'message' => 'Không tìm thấy thông tin tài khoản hoặc nhân vật!'
                ];
            }

            // Send items to character via mail system
            $mailResult = $this->sendItemsToCharacter($giftcode, $characterInfo, $zoneId);
            if (!$mailResult['success']) {
                return [
                    'success' => false,
                    'message' => 'Lỗi khi gửi vật phẩm: ' . $mailResult['message']
                ];
            }

            // Log the usage
            GiftcodeLog::createLog(
                $userId,
                $characterId,
                $zoneId,
                $giftcodeString,
                $giftcode->id,
                $characterInfo->rname,
                $userInfo->UserName
            );

            // Update usage count
            $giftcode->increment('used_count');

            return [
                'success' => true,
                'message' => 'Sử dụng giftcode thành công! Vật phẩm đã được gửi vào hòm thư của nhân vật.',
                'items' => $this->formatItemsForDisplay($giftcode->items),
                'mail_id' => $mailResult['mail_id']
            ];

        } catch (Exception $e) {
            \Log::error('Giftcode usage error: ' . $e->getMessage(), [
                'giftcode' => $giftcodeString,
                'user_id' => $userId,
                'character_id' => $characterId,
                'zone_id' => $zoneId
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra khi sử dụng giftcode. Vui lòng thử lại sau!'
            ];
        }
    }

    /**
     * Find giftcode by code string with caching
     */
    private function findGiftcode($codeString)
    {
        // Cache key for this giftcode
        $cacheKey = "giftcode_lookup_{$codeString}";

        return Cache::remember($cacheKey, 300, function() use ($codeString) {
            // Try to find by single code using LIKE (compatible with older MySQL)
            $giftcode = Giftcode::where('code', 'like', '%"' . $codeString . '"%')->first();

            if (!$giftcode) {
                // Try alternative search methods for older MySQL versions
                $giftcode = Giftcode::where('code', 'like', '%' . $codeString . '%')->first();
            }

            return $giftcode;
        });
    }

    /**
     * Validate if giftcode can be used
     */
    private function validateGiftcode($giftcode, $userId, $characterId, $zoneId)
    {
        // Check if giftcode is active
        if (!$giftcode->is_active) {
            return ['valid' => false, 'message' => 'Giftcode đã bị vô hiệu hóa!'];
        }

        // Check expiry
        if ($giftcode->isExpired()) {
            return ['valid' => false, 'message' => 'Giftcode đã hết hạn!'];
        }

        // Check usage limit
        if ($giftcode->isUsedUp()) {
            return ['valid' => false, 'message' => 'Giftcode đã hết lượt sử dụng!'];
        }

        // Check zone restriction
        if ($giftcode->zoneid > 0 && $giftcode->zoneid != $zoneId) {
            return ['valid' => false, 'message' => 'Giftcode không áp dụng cho server này!'];
        }

        // Check for user-specific restrictions (Temporarily Disabled)
        /*
        if ($giftcode->restriction_type === 'specific_users') {
            $allowedUsernames = json_decode($giftcode->allowed_usernames, true);
            $user = $this->getUserInfo($userId);

            if (!is_array($allowedUsernames) || !in_array($user->UserName, $allowedUsernames)) {
                return ['valid' => false, 'message' => 'Tài khoản của bạn không được phép sử dụng giftcode này!'];
            }
        }
        */

        // Check if user has already used this giftcode
        $alreadyUsed = GiftcodeLog::where('uid', $userId)
            ->where('groupid', $giftcode->id)
            ->exists();

        if ($alreadyUsed) {
            return ['valid' => false, 'message' => 'Bạn đã sử dụng giftcode này rồi!'];
        }

        return ['valid' => true, 'message' => 'OK'];
    }

    /**
     * Get user info from main database
     */
    private function getUserInfo($userId)
    {
        return DB::table('t_account')
            ->where('ID', $userId)
            ->first();
    }

    /**
     * Get character info from game database
     */
    private function getCharacterInfo($characterId, $zoneId)
    {
        return DB::connection('game_mysql')
            ->table('t_roles')
            ->where('rid', $characterId)
            ->where('isdel', 0) // Not deleted
            ->first();
    }

    /**
     * Send items to character via mail system
     */
    private function sendItemsToCharacter($giftcode, $character, $zoneId)
    {
        try {
            // Create mail entry
            $mailId = DB::connection('game_mysql')->table('t_mail')->insertGetId([
                'senderrid' => 0,
                'senderrname' => 'GM',
                'sendtime' => Carbon::now(),
                'receiverrid' => $character->rid,
                'reveiverrname' => $character->rname,
                'readtime' => '1900-01-01 12:00:00',
                'isread' => 0,
                'mailtype' => 0,
                'hasfetchattachment' => 0,
                'subject' => 'Phần thưởng Giftcode',
                'content' => $giftcode->content ?: 'Bạn đã nhận được phần thưởng từ giftcode!',
                'yinliang' => 0,
                'tongqian' => 0,
                'yuanbao' => 0
            ]);

            // Add items to mail
            if ($giftcode->items && is_array($giftcode->items)) {
                $mailItems = [];
                foreach ($giftcode->items as $item) {
                    $parts = explode(',', $item);
                    if (count($parts) >= 7) {
                        $mailItems[] = [
                            'mailid' => $mailId,
                            'goodsid' => (int)$parts[0],
                            'gcount' => (int)$parts[1],
                            'binding' => (int)$parts[2],
                            'forge_level' => (int)$parts[3],
                            'appendproplev' => (int)$parts[4],
                            'lucky' => (int)$parts[5],
                            'excellenceinfo' => (int)$parts[6],
                        ];
                    }
                }

                if (!empty($mailItems)) {
                    DB::connection('game_mysql')->table('t_mailgoods')->insert($mailItems);
                }
            }

            return [
                'success' => true,
                'mail_id' => $mailId,
                'message' => 'Items sent successfully'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Format items for display
     */
    private function formatItemsForDisplay($items)
    {
        if (!$items || !is_array($items)) {
            return [];
        }

        $formatted = [];
        foreach ($items as $item) {
            $parts = explode(',', $item);
            if (count($parts) >= 2) {
                $formatted[] = [
                    'id' => $parts[0],
                    'name' => $this->getItemName($parts[0]), // You can implement item name lookup
                    'count' => $parts[1],
                    'binding' => $parts[2] ?? 0
                ];
            }
        }

        return $formatted;
    }

    /**
     * Get item name by ID (implement based on your item database)
     */
    private function getItemName($itemId)
    {
        // This should be implemented based on your item database structure
        // For now, return a generic name
        return "Item #{$itemId}";
    }
}
