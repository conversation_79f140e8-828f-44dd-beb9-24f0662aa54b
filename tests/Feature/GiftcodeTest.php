<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\Giftcode;
use App\Models\GiftcodeLog;
use App\Services\GiftcodeService;
use Illuminate\Support\Facades\DB;

class GiftcodeTest extends TestCase
{
    use RefreshDatabase;

    protected $giftcodeService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->giftcodeService = app(GiftcodeService::class);
    }

    /** @test */
    public function it_can_create_single_giftcode()
    {
        $data = [
            'type' => 1, // Public
            'accounts' => null,
            'multiple' => false,
            'code' => 'TEST2025',
            'items' => ['14,5,1,0,0,0,0', '15,10,1,0,0,0,0'],
            'content' => 'Test giftcode',
            'limit' => 100,
            'period' => 30,
            'zoneid' => 1
        ];

        $result = $this->giftcodeService->createGiftcode($data);

        $this->assertTrue($result['success']);
        $this->assertInstanceOf(Giftcode::class, $result['giftcode']);
        $this->assertEquals(['TEST2025'], $result['codes']);
        
        // Check database
        $this->assertDatabaseHas('t_giftcode', [
            'type' => 1,
            'content' => 'Test giftcode',
            'limit' => 100,
            'period' => 30,
            'zoneid' => 1
        ]);
    }

    /** @test */
    public function it_can_create_multiple_giftcodes()
    {
        $data = [
            'type' => 1,
            'accounts' => null,
            'multiple' => true,
            'number' => 5,
            'items' => ['14,5,1,0,0,0,0'],
            'content' => 'Multiple test giftcode',
            'limit' => 50,
            'period' => 0,
            'zoneid' => 0
        ];

        $result = $this->giftcodeService->createGiftcode($data);

        $this->assertTrue($result['success']);
        $this->assertCount(5, $result['codes']);
        
        // Check that all codes are unique
        $this->assertEquals(5, count(array_unique($result['codes'])));
    }

    /** @test */
    public function it_can_validate_giftcode_usage()
    {
        // Create a test giftcode
        $giftcode = Giftcode::create([
            'type' => 1,
            'accounts' => null,
            'multiple' => false,
            'code' => ['TESTCODE'],
            'items' => ['14,5,1,0,0,0,0'],
            'content' => 'Test validation',
            'limit' => 1,
            'period' => 30,
            'zoneid' => 1
        ]);

        // Test valid usage
        $this->assertTrue($giftcode->canBeUsedByUser(1, 1));

        // Create usage log
        GiftcodeLog::createLog(1, 1, 1, 'TESTCODE', $giftcode->id);

        // Test that it can't be used again
        $this->assertFalse($giftcode->canBeUsedByUser(1, 1));
    }

    /** @test */
    public function it_can_check_expiry()
    {
        // Create expired giftcode
        $expiredGiftcode = Giftcode::create([
            'type' => 1,
            'accounts' => null,
            'multiple' => false,
            'code' => ['EXPIRED'],
            'items' => ['14,5,1,0,0,0,0'],
            'content' => 'Expired test',
            'limit' => 100,
            'period' => 1, // 1 day
            'zoneid' => 1,
            'created_at' => now()->subDays(2) // Created 2 days ago
        ]);

        $this->assertTrue($expiredGiftcode->isExpired());

        // Create non-expired giftcode
        $validGiftcode = Giftcode::create([
            'type' => 1,
            'accounts' => null,
            'multiple' => false,
            'code' => ['VALID'],
            'items' => ['14,5,1,0,0,0,0'],
            'content' => 'Valid test',
            'limit' => 100,
            'period' => 30, // 30 days
            'zoneid' => 1
        ]);

        $this->assertFalse($validGiftcode->isExpired());
    }

    /** @test */
    public function it_can_check_usage_limit()
    {
        $giftcode = Giftcode::create([
            'type' => 1,
            'accounts' => null,
            'multiple' => false,
            'code' => ['LIMITED'],
            'items' => ['14,5,1,0,0,0,0'],
            'content' => 'Limited test',
            'limit' => 2, // Only 2 uses allowed
            'period' => 0,
            'zoneid' => 1
        ]);

        $this->assertFalse($giftcode->isUsedUp());

        // Add usage logs
        GiftcodeLog::createLog(1, 1, 1, 'LIMITED', $giftcode->id);
        GiftcodeLog::createLog(2, 2, 1, 'LIMITED', $giftcode->id);

        $this->assertTrue($giftcode->isUsedUp());
    }

    /** @test */
    public function it_can_handle_private_giftcode()
    {
        $giftcode = Giftcode::create([
            'type' => 2, // Private
            'accounts' => 'testuser1,testuser2',
            'multiple' => false,
            'code' => ['PRIVATE'],
            'items' => ['14,5,1,0,0,0,0'],
            'content' => 'Private test',
            'limit' => 100,
            'period' => 0,
            'zoneid' => 1
        ]);

        $allowedAccounts = $giftcode->getAllowedAccounts();
        $this->assertEquals(['testuser1', 'testuser2'], $allowedAccounts);
    }
}
