<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\DB;

echo "=== Test Hệ thống Monthly Card ===\n\n";

try {
    // Test 1: Kiểm tra cấu hình giá tiền
    echo "1. <PERSON><PERSON>m tra cấu hình Monthly Card...\n";
    
    // Simulate getting package config
    $packages = [
        'premium' => [
            'name' => 'Thẻ Tháng Premium',
            'cost_coins' => 1500000, // Updated to 1,500,000 coins
            'rmb_amount' => 45, // RMB amount for game server
            'duration_days' => 30,
            'daily_reward_coins' => 15000,
            'description' => 'Thẻ tháng cao cấp - Nhận 15,000 coin + items đặc biệt mỗi ngày trong 30 ngày',
            'bonus_items' => [
                '14,20,1,0,0,0,0', // Jewel of Bless x20
                '15,50,1,0,0,0,0', // Jewel of Soul x50
                '16,5,1,0,0,0,0',  // Jewel of Life x5
                '12,2,1,0,0,0,0'   // Box of Luck x2
            ],
            'daily_items' => [
                '14,5,1,0,0,0,0', // Jewel of Bless x5 daily
                '15,3,1,0,0,0,0'  // Jewel of Soul x3 daily
            ]
        ]
    ];
    
    $package = $packages['premium'];
    echo "✓ Package: {$package['name']}\n";
    echo "✓ Cost: " . number_format($package['cost_coins']) . " coins\n";
    echo "✓ RMB Amount: {$package['rmb_amount']} RMB\n";
    echo "✓ Duration: {$package['duration_days']} days\n";
    echo "✓ Daily Reward: " . number_format($package['daily_reward_coins']) . " coins\n";
    
    echo "\n";

    // Test 2: Tạo test user và character
    echo "2. Tạo test data...\n";
    
    // Tạo test account nếu chưa có
    $testAccount = DB::table('t_account')->where('UserName', 'testcard')->first();
    if (!$testAccount) {
        DB::table('t_account')->insert([
            'UserName' => 'testcard',
            'Password' => 'test123',
            'Email' => '<EMAIL>',
            'CreateTime' => now(),
            'LastLoginTime' => now(),
            'Status' => 1
        ]);
        $testUserId = DB::connection()->getPdo()->lastInsertId();
        echo "✓ Tạo test account: testcard (ID: $testUserId)\n";
    } else {
        $testUserId = $testAccount->ID;
        echo "✓ Sử dụng test account có sẵn: testcard (ID: $testUserId)\n";
    }
    
    // Tạo user_coins record với đủ tiền
    $userCoins = DB::table('user_coins')->where('account_id', $testUserId)->first();
    if (!$userCoins) {
        DB::table('user_coins')->insert([
            'account_id' => $testUserId,
            'username' => 'testcard',
            'coins' => 2000000, // Đủ tiền để mua thẻ tháng
            'total_recharged' => 2000000,
            'total_spent' => 0,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        echo "✓ Tạo user_coins với 2,000,000 coins\n";
    } else {
        // Cập nhật để đảm bảo đủ tiền
        DB::table('user_coins')
            ->where('account_id', $testUserId)
            ->update([
                'coins' => 2000000,
                'updated_at' => now()
            ]);
        echo "✓ Cập nhật user_coins với 2,000,000 coins\n";
    }
    
    // Tạo test character
    $testCharacter = DB::connection('game_mysql')->table('t_roles')->where('rname', 'TestCardChar')->first();
    if (!$testCharacter) {
        $gameUserId = 'ZT' . str_pad($testUserId, 4, '0', STR_PAD_LEFT);
        DB::connection('game_mysql')->table('t_roles')->insert([
            'userid' => $gameUserId,
            'rname' => 'TestCardChar',
            'occupation' => 1,
            'level' => 1,
            'experience' => 0,
            'pkvalue' => 0,
            'money1' => 0,
            'money2' => 0,
            'isdel' => 0,
            'regtime' => now(),
            'lasttime' => now(),
            'zoneid' => 1
        ]);
        $testCharacterId = DB::connection('game_mysql')->getPdo()->lastInsertId();
        echo "✓ Tạo test character: TestCardChar (ID: $testCharacterId)\n";
    } else {
        $testCharacterId = $testCharacter->rid;
        echo "✓ Sử dụng test character có sẵn: TestCardChar (ID: $testCharacterId)\n";
    }
    
    echo "\n";

    // Test 3: Simulate mua thẻ tháng
    echo "3. Test mua thẻ tháng...\n";
    
    DB::beginTransaction();
    
    try {
        // Step 1: Validation
        $userCoins = DB::table('user_coins')->where('account_id', $testUserId)->first();
        if (!$userCoins || $userCoins->coins < $package['cost_coins']) {
            throw new Exception('Không đủ coin');
        }
        echo "✓ Validation passed - User có " . number_format($userCoins->coins) . " coins\n";

        // Step 2: Deduct coins
        $balanceBefore = $userCoins->coins;
        $balanceAfter = $balanceBefore - $package['cost_coins'];
        
        DB::table('user_coins')
            ->where('account_id', $testUserId)
            ->update([
                'coins' => $balanceAfter,
                'total_spent' => DB::raw('total_spent + ' . $package['cost_coins']),
                'updated_at' => now()
            ]);
        echo "✓ Trừ " . number_format($package['cost_coins']) . " coins - Balance: " . number_format($balanceAfter) . "\n";

        // Step 3: Create monthly card purchase record
        $activatedAt = now();
        $expiresAt = $activatedAt->copy()->addDays($package['duration_days']);
        
        $purchaseId = DB::table('monthly_card_purchases')->insertGetId([
            'user_id' => $testUserId,
            'package_name' => $package['name'],
            'package_type' => 'premium',
            'duration_days' => $package['duration_days'],
            'cost_coins' => $package['cost_coins'],
            'daily_reward_coins' => $package['daily_reward_coins'],
            'bonus_items' => json_encode($package['bonus_items'] ?? []),
            'daily_items' => json_encode($package['daily_items'] ?? []),
            'status' => 'active',
            'activated_at' => $activatedAt,
            'expires_at' => $expiresAt,
            'days_claimed' => 0,
            'ip_address' => '127.0.0.1',
            'created_at' => now(),
            'updated_at' => now()
        ]);
        echo "✓ Tạo monthly card purchase record (ID: $purchaseId)\n";

        // Step 4: Update game server database (t_tempmoney)
        $gameUserId = 'ZT' . str_pad($testUserId, 4, '0', STR_PAD_LEFT);

        DB::connection('game_mysql')->table('t_tempmoney')->insert([
            'uid' => $gameUserId,
            'addmoney' => $package['rmb_amount'] // RMB amount
        ]);
        echo "✓ Tạo t_tempmoney record - UID: $gameUserId, Money: {$package['rmb_amount']}\n";

        // Step 5: Log transaction in t_history
        DB::table('t_history')->insert([
            'uid' => $testUserId,
            'rid' => $testCharacterId,
            'zoneid' => 1,
            'type' => 4, // 4 = monthly card
            'balance' => $balanceAfter,
            'content' => json_encode([
                'money' => $package['cost_coins'],
                'package_type' => 'premium',
                'package_name' => $package['name'],
                'rmb_amount' => $package['rmb_amount'],
                'character_name' => 'TestCardChar'
            ]),
            'created_at' => now(),
            'updated_at' => now()
        ]);
        echo "✓ Tạo t_history log\n";

        DB::commit();
        echo "✓ Transaction completed successfully!\n";
        
    } catch (Exception $e) {
        DB::rollback();
        echo "✗ Transaction failed: " . $e->getMessage() . "\n";
    }
    
    echo "\n";

    // Test 4: Kiểm tra kết quả
    echo "4. Kiểm tra kết quả...\n";
    
    // Check user coins
    $finalCoins = DB::table('user_coins')->where('account_id', $testUserId)->first();
    echo "✓ Final balance: " . number_format($finalCoins->coins) . " coins\n";
    echo "✓ Total spent: " . number_format($finalCoins->total_spent) . " coins\n";
    
    // Check monthly card purchase
    $purchase = DB::table('monthly_card_purchases')->where('user_id', $testUserId)->latest()->first();
    if ($purchase) {
        echo "✓ Monthly card purchase found\n";
        echo "  - Package: {$purchase->package_name}\n";
        echo "  - Status: {$purchase->status}\n";
        echo "  - Expires: {$purchase->expires_at}\n";
    }
    
    // Check t_tempmoney
    $tempMoney = DB::connection('game_mysql')->table('t_tempmoney')
        ->where('uid', 'ZT' . str_pad($testUserId, 4, '0', STR_PAD_LEFT))
        ->orderBy('id', 'desc')
        ->first();
    if ($tempMoney) {
        echo "✓ t_tempmoney record found\n";
        echo "  - ID: {$tempMoney->id}\n";
        echo "  - UID: {$tempMoney->uid}\n";
        echo "  - Add Money: {$tempMoney->addmoney} RMB\n";
    } else {
        echo "✗ t_tempmoney record not found\n";
    }
    
    // Check t_history
    $history = DB::table('t_history')->where('uid', $testUserId)->latest()->first();
    if ($history) {
        echo "✓ t_history record found\n";
        echo "  - Type: {$history->type}\n";
        echo "  - Balance: " . number_format($history->balance) . "\n";
        echo "  - Content: {$history->content}\n";
    }
    
    echo "\n=== Test hoàn thành ===\n";

} catch (Exception $e) {
    echo "✗ Lỗi: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
