@extends('layouts.admin')

@section('title', 'Tạo Giftcode mới')

@section('content')
<div class="giftcode-create-container">
    <!-- Header Section -->
    <div class="page-header-modern">
        <div class="header-content">
            <div class="header-icon">
                <i class="fas fa-gift"></i>
            </div>
            <div class="header-text">
                <h1 class="page-title">Tạo Giftcode Mới</h1>
                <p class="page-subtitle">Tạo mã quà tặng cho người chơi MU Online</p>
            </div>
        </div>
        <div class="header-actions">
            <a href="{{ route('admin.giftcode.index') }}" class="btn btn-glass">
                <i class="fas fa-arrow-left"></i>
                <span>Quay lại</span>
            </a>
        </div>
    </div>

    <form method="POST" action="{{ route('admin.giftcode.store') }}" class="giftcode-form">
        @csrf

        <!-- Basic Information Card -->
        <div class="form-card">
            <div class="card-header-modern">
                <div class="card-icon">
                    <i class="fas fa-info-circle"></i>
                </div>
                <h3 class="card-title">Thông tin cơ bản</h3>
            </div>
            <div class="card-body-modern">
                <div class="form-grid">
                    <div class="form-group-modern">
                        <label for="name" class="form-label-modern">
                            <i class="fas fa-tag"></i>
                            Tên Giftcode
                            <span class="required">*</span>
                        </label>
                        <input type="text"
                               class="form-input-modern @error('name') is-invalid @enderror"
                               id="name"
                               name="name"
                               value="{{ old('name') }}"
                               placeholder="Ví dụ: Giftcode Tết 2025">
                        @error('name')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group-modern">
                        <label for="max_uses" class="form-label-modern">
                            <i class="fas fa-users"></i>
                            Số lần sử dụng tối đa
                            <span class="required">*</span>
                        </label>
                        <input type="number"
                               class="form-input-modern @error('max_uses') is-invalid @enderror"
                               id="max_uses"
                               name="max_uses"
                               value="{{ old('max_uses', 1) }}"
                               min="1"
                               max="10000"
                               placeholder="1000">
                        @error('max_uses')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="form-group-modern full-width">
                    <label for="description" class="form-label-modern">
                        <i class="fas fa-align-left"></i>
                        Mô tả
                    </label>
                    <textarea class="form-textarea-modern @error('description') is-invalid @enderror"
                              id="description"
                              name="description"
                              rows="3"
                              placeholder="Mô tả chi tiết về giftcode này...">{{ old('description') }}</textarea>
                    @error('description')
                        <div class="error-message">{{ $message }}</div>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Code Generation Card -->
        <div class="form-card">
            <div class="card-header-modern">
                <div class="card-icon">
                    <i class="fas fa-code"></i>
                </div>
                <h3 class="card-title">Tạo mã Code</h3>
            </div>
            <div class="card-body-modern">
                <div class="radio-group-modern">
                    <label class="form-label-modern">
                        <i class="fas fa-cogs"></i>
                        Loại tạo code
                        <span class="required">*</span>
                    </label>
                    <div class="radio-options">
                        <div class="radio-option">
                            <input type="radio"
                                   name="code_type"
                                   id="single_code"
                                   value="single"
                                   {{ old('code_type', 'single') == 'single' ? 'checked' : '' }}>
                            <label for="single_code" class="radio-label">
                                <div class="radio-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div class="radio-content">
                                    <span class="radio-title">Code duy nhất</span>
                                    <span class="radio-desc">Tạo một mã code đơn lẻ</span>
                                </div>
                            </label>
                        </div>
                        <div class="radio-option">
                            <input type="radio"
                                   name="code_type"
                                   id="multiple_codes"
                                   value="multiple"
                                   {{ old('code_type') == 'multiple' ? 'checked' : '' }}>
                            <label for="multiple_codes" class="radio-label">
                                <div class="radio-icon">
                                    <i class="fas fa-copy"></i>
                                </div>
                                <div class="radio-content">
                                    <span class="radio-title">Nhiều code</span>
                                    <span class="radio-desc">Tạo nhiều mã code cùng lúc</span>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Single Code Section -->
                <div id="single_code_section" class="code-section">
                    <div class="form-group-modern">
                        <label for="code" class="form-label-modern">
                            <i class="fas fa-key"></i>
                            Mã Code
                            <span class="required">*</span>
                        </label>
                        <input type="text"
                               class="form-input-modern @error('code') is-invalid @enderror"
                               id="code"
                               name="code"
                               value="{{ old('code') }}"
                               placeholder="Ví dụ: MUTET2025">
                        @error('code')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Multiple Codes Section -->
                <div id="multiple_codes_section" class="code-section" style="display: none;">
                    <div class="form-grid">
                        <div class="form-group-modern">
                            <label for="code_prefix" class="form-label-modern">
                                <i class="fas fa-hashtag"></i>
                                Tiền tố Code
                                <span class="required">*</span>
                            </label>
                            <input type="text"
                                   class="form-input-modern @error('code_prefix') is-invalid @enderror"
                                   id="code_prefix"
                                   name="code_prefix"
                                   value="{{ old('code_prefix') }}"
                                   placeholder="Ví dụ: MUGIFT">
                            <small class="form-hint">Chỉ được chứa chữ cái, số và dấu gạch dưới</small>
                            @error('code_prefix')
                                <div class="error-message">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="form-group-modern">
                            <label for="code_count" class="form-label-modern">
                                <i class="fas fa-sort-numeric-up"></i>
                                Số lượng Code
                                <span class="required">*</span>
                            </label>
                            <input type="number"
                                   class="form-input-modern @error('code_count') is-invalid @enderror"
                                   id="code_count"
                                   name="code_count"
                                   value="{{ old('code_count', 10) }}"
                                   min="1"
                                   max="1000"
                                   placeholder="100">
                            <small class="form-hint">Sẽ tạo: MUGIFT0001, MUGIFT0002, ...</small>
                            @error('code_count')
                                <div class="error-message">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Usage Restriction Card -->
        <div class="form-card">
            <div class="card-header-modern">
                <div class="card-icon">
                    <i class="fas fa-user-lock"></i>
                </div>
                <h3 class="card-title">Hạn chế sử dụng</h3>
            </div>
            <div class="card-body-modern">
                <div class="radio-group-modern">
                    <label class="form-label-modern">
                        <i class="fas fa-shield-alt"></i>
                        Loại giới hạn
                        <span class="required">*</span>
                    </label>
                    <div class="radio-options">
                        <div class="radio-option">
                            <input type="radio"
                                   name="restriction_type"
                                   id="restriction_none"
                                   value="none"
                                   {{ old('restriction_type', 'none') == 'none' ? 'checked' : '' }}>
                            <label for="restriction_none" class="radio-label">
                                <div class="radio-icon">
                                    <i class="fas fa-globe"></i>
                                </div>
                                <div class="radio-content">
                                    <span class="radio-title">Công khai</span>
                                    <span class="radio-desc">Mọi người đều có thể sử dụng</span>
                                </div>
                            </label>
                        </div>
                        <div class="radio-option">
                            <input type="radio"
                                   name="restriction_type"
                                   id="restriction_specific_users"
                                   value="specific_users"
                                   {{ old('restriction_type') == 'specific_users' ? 'checked' : '' }}>
                            <label for="restriction_specific_users" class="radio-label">
                                <div class="radio-icon">
                                    <i class="fas fa-users-cog"></i>
                                </div>
                                <div class="radio-content">
                                    <span class="radio-title">Chỉ định tài khoản</span>
                                    <span class="radio-desc">Chỉ những tài khoản được liệt kê mới có thể sử dụng</span>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Specific Users Section -->
                <div id="specific_users_section" style="display: none;">
                    <div class="form-group-modern full-width">
                        <label for="allowed_usernames" class="form-label-modern">
                            <i class="fas fa-list-ul"></i>
                            Danh sách tài khoản được phép
                            <span class="required">*</span>
                        </label>
                        <textarea class="form-textarea-modern @error('allowed_usernames') is-invalid @enderror"
                                  id="allowed_usernames"
                                  name="allowed_usernames"
                                  rows="5"
                                  placeholder="Nhập danh sách tên tài khoản, mỗi tài khoản một dòng hoặc cách nhau bằng dấu phẩy.">{{ old('allowed_usernames') }}</textarea>
                        <small class="form-hint">Phân tách các tên tài khoản bằng dấu phẩy, dấu cách hoặc xuống dòng.</small>
                        @error('allowed_usernames')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>

        <!-- Rewards Card -->
        <div class="form-card">
            <div class="card-header-modern">
                <div class="card-icon">
                    <i class="fas fa-gift"></i>
                </div>
                <h3 class="card-title">Phần thưởng</h3>
            </div>
            <div class="card-body-modern">
                <div class="radio-group-modern">
                    <label class="form-label-modern">
                        <i class="fas fa-treasure-chest"></i>
                        Loại phần thưởng
                        <span class="required">*</span>
                    </label>
                    <div class="radio-options">
                        <div class="radio-option">
                            <input type="radio"
                                   name="reward_type"
                                   id="reward_coins"
                                   value="coins"
                                   {{ old('reward_type', 'coins') == 'coins' ? 'checked' : '' }}>
                            <label for="reward_coins" class="radio-label">
                                <div class="radio-icon coin-icon">
                                    <i class="fas fa-coins"></i>
                                </div>
                                <div class="radio-content">
                                    <span class="radio-title">Chỉ Coin</span>
                                    <span class="radio-desc">Tặng tiền xu trong game</span>
                                </div>
                            </label>
                        </div>
                        <div class="radio-option">
                            <input type="radio"
                                   name="reward_type"
                                   id="reward_items"
                                   value="items"
                                   {{ old('reward_type') == 'items' ? 'checked' : '' }}>
                            <label for="reward_items" class="radio-label">
                                <div class="radio-icon item-icon">
                                    <i class="fas fa-gem"></i>
                                </div>
                                <div class="radio-content">
                                    <span class="radio-title">Chỉ Item</span>
                                    <span class="radio-desc">Tặng vật phẩm trong game</span>
                                </div>
                            </label>
                        </div>
                        <div class="radio-option">
                            <input type="radio"
                                   name="reward_type"
                                   id="reward_mixed"
                                   value="mixed"
                                   {{ old('reward_type') == 'mixed' ? 'checked' : '' }}>
                            <label for="reward_mixed" class="radio-label">
                                <div class="radio-icon mixed-icon">
                                    <i class="fas fa-star"></i>
                                </div>
                                <div class="radio-content">
                                    <span class="radio-title">Cả hai</span>
                                    <span class="radio-desc">Tặng cả coin và item</span>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Coin Reward Section -->
                <div id="coin_reward_section" class="reward-section">
                    <div class="form-group-modern">
                        <label for="reward_coins_input" class="form-label-modern">
                            <i class="fas fa-coins"></i>
                            Số Coin thưởng
                            <span class="required">*</span>
                        </label>
                        <input type="number"
                               class="form-input-modern coin-input @error('reward_coins') is-invalid @enderror"
                               id="reward_coins_input"
                               name="reward_coins"
                               value="{{ old('reward_coins', 1000) }}"
                               min="0"
                               placeholder="1000000">
                        <small class="form-hint">Số coin sẽ được thêm vào tài khoản người chơi</small>
                        @error('reward_coins')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Item Reward Section -->
                <div id="item_reward_section" class="reward-section" style="display: none;">
                    <!-- Item Selection Interface -->
                    <div class="item-selection-container">
                        <div class="item-selection-header">
                            <h4 class="selection-title">
                                <i class="fas fa-gem"></i>
                                Chọn Item Thưởng
                            </h4>
                            <div class="selection-controls">
                                <input type="text"
                                       id="item_search"
                                       class="form-input-modern search-input"
                                       placeholder="🔍 Tìm kiếm item...">
                                <select id="item_category" class="form-input-modern category-select">
                                    <option value="">Tất cả danh mục</option>
                                    <option value="Jewels">💎 Jewels</option>
                                    <option value="Potions">🧪 Potions</option>
                                    <option value="Wings">🪶 Wings</option>
                                    <option value="Currency">💰 Currency</option>
                                    <option value="Feathers">🪶 Feathers</option>
                                    <option value="Upgrade Materials">🗿 Upgrade Materials</option>
                                    <option value="Event Items">⭐ Event Items</option>
                                </select>
                            </div>
                        </div>

                        <!-- Popular Items Quick Select -->
                        <div class="popular-items-section">
                            <h5 class="popular-title">
                                <i class="fas fa-star"></i>
                                Item phổ biến
                            </h5>
                            <div class="popular-items-grid" id="popular_items_grid">
                                <!-- Will be populated by JavaScript -->
                            </div>
                        </div>

                        <!-- Item Browser -->
                        <div class="item-browser">
                            <div class="item-grid" id="item_grid">
                                <!-- Will be populated by JavaScript -->
                            </div>
                        </div>

                        <!-- Selected Items -->
                        <div class="selected-items-section">
                            <h5 class="selected-title">
                                <i class="fas fa-shopping-cart"></i>
                                Item đã chọn
                            </h5>
                            <div class="selected-items-list" id="selected_items_list">
                                <div class="empty-selection">
                                    <i class="fas fa-inbox"></i>
                                    <p>Chưa có item nào được chọn</p>
                                </div>
                            </div>
                        </div>

                        <!-- Hidden textarea for form submission -->
                        <textarea class="form-textarea-modern item-textarea @error('reward_items') is-invalid @enderror"
                                  id="reward_items"
                                  name="reward_items"
                                  rows="6"
                                  style="display: none;"
                                  placeholder="Mỗi dòng một item theo format: item_id,quantity,name&#10;Ví dụ:&#10;14,5,Jewel of Bless&#10;15,10,Jewel of Soul&#10;16,1,Jewel of Life">{{ old('reward_items') }}</textarea>

                        @error('reward_items')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>

        <!-- Expiry & Settings Card -->
        <div class="form-card">
            <div class="card-header-modern">
                <div class="card-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <h3 class="card-title">Cài đặt thời hạn</h3>
            </div>
            <div class="card-body-modern">
                <div class="form-group-modern">
                    <label for="expires_at" class="form-label-modern">
                        <i class="fas fa-calendar-alt"></i>
                        Ngày hết hạn
                    </label>
                    <input type="datetime-local"
                           class="form-input-modern @error('expires_at') is-invalid @enderror"
                           id="expires_at"
                           name="expires_at"
                           value="{{ old('expires_at') }}">
                    <small class="form-hint">Để trống nếu giftcode không có thời hạn</small>
                    @error('expires_at')
                        <div class="error-message">{{ $message }}</div>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Submit Actions -->
        <div class="form-actions">
            <button type="submit" class="btn btn-primary-modern">
                <i class="fas fa-magic"></i>
                <span>Tạo Giftcode</span>
            </button>
            <a href="{{ route('admin.giftcode.index') }}" class="btn btn-secondary-modern">
                <i class="fas fa-times"></i>
                <span>Hủy bỏ</span>
            </a>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle code type change with smooth animations
    const codeTypeRadios = document.querySelectorAll('input[name="code_type"]');
    const singleCodeSection = document.getElementById('single_code_section');
    const multipleCodesSection = document.getElementById('multiple_codes_section');

    function toggleCodeSections() {
        const selectedType = document.querySelector('input[name="code_type"]:checked').value;

        // Add fade out effect
        singleCodeSection.style.opacity = '0';
        multipleCodesSection.style.opacity = '0';

        setTimeout(() => {
            if (selectedType === 'single') {
                singleCodeSection.style.display = 'block';
                multipleCodesSection.style.display = 'none';
                setTimeout(() => singleCodeSection.style.opacity = '1', 50);
            } else {
                singleCodeSection.style.display = 'none';
                multipleCodesSection.style.display = 'block';
                setTimeout(() => multipleCodesSection.style.opacity = '1', 50);
            }
        }, 150);
    }

    codeTypeRadios.forEach(radio => {
        radio.addEventListener('change', toggleCodeSections);
    });

    // Handle restriction type change
    const restrictionTypeRadios = document.querySelectorAll('input[name="restriction_type"]');
    const specificUsersSection = document.getElementById('specific_users_section');

    function toggleRestrictionSection() {
        if (document.querySelector('input[name="restriction_type"]:checked').value === 'specific_users') {
            specificUsersSection.style.display = 'block';
        } else {
            specificUsersSection.style.display = 'none';
        }
    }

    restrictionTypeRadios.forEach(radio => {
        radio.addEventListener('change', toggleRestrictionSection);
    });

    // Initial check on page load
    toggleRestrictionSection();

    // Handle reward type change with smooth animations
    const rewardTypeRadios = document.querySelectorAll('input[name="reward_type"]');
    const coinRewardSection = document.getElementById('coin_reward_section');
    const itemRewardSection = document.getElementById('item_reward_section');

    function toggleRewardSections() {
        const selectedType = document.querySelector('input[name="reward_type"]:checked').value;

        // Add fade out effect
        coinRewardSection.style.opacity = '0';
        itemRewardSection.style.opacity = '0';

        setTimeout(() => {
            const showCoin = selectedType === 'coins' || selectedType === 'mixed';
            const showItem = selectedType === 'items' || selectedType === 'mixed';

            coinRewardSection.style.display = showCoin ? 'block' : 'none';
            itemRewardSection.style.display = showItem ? 'block' : 'none';

            setTimeout(() => {
                if (showCoin) coinRewardSection.style.opacity = '1';
                if (showItem) itemRewardSection.style.opacity = '1';
            }, 50);
        }, 150);
    }

    rewardTypeRadios.forEach(radio => {
        radio.addEventListener('change', toggleRewardSections);
    });

    // Initialize sections
    toggleCodeSections();
    toggleRewardSections();

    // Add number formatting for coin input
    const coinInput = document.getElementById('reward_coins_input');
    if (coinInput) {
        coinInput.addEventListener('input', function() {
            let value = this.value.replace(/,/g, '');
            if (!isNaN(value) && value !== '') {
                this.value = parseInt(value).toLocaleString();
            }
        });
    }

    // Form validation feedback
    const form = document.querySelector('.giftcode-form');
    form.addEventListener('submit', function(e) {
        const submitBtn = form.querySelector('.btn-primary-modern');
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>Đang tạo...</span>';
        submitBtn.disabled = true;
    });

    // Item selection functionality
    let selectedItems = [];
    let allItems = {};

    // Load items from server
    async function loadItems() {
        try {
            const response = await fetch('/admin/giftcode/api/items');
            const data = await response.json();
            if (data.success) {
                allItems = data.items;
                displayPopularItems();
                displayAllItems();
            }
        } catch (error) {
            console.error('Error loading items:', error);
        }
    }

    // Display popular items
    function displayPopularItems() {
        const popularIds = [14, 15, 16, 22, 31, 260, 261, 262, 700, 701, 46, 47, 3, 6];
        const popularGrid = document.getElementById('popular_items_grid');
        popularGrid.innerHTML = '';

        popularIds.forEach(id => {
            if (allItems[id]) {
                const item = allItems[id];
                const itemCard = createItemCard(id, item, true);
                popularGrid.appendChild(itemCard);
            }
        });
    }

    // Display all items
    function displayAllItems() {
        const itemGrid = document.getElementById('item_grid');
        itemGrid.innerHTML = '';

        Object.entries(allItems).forEach(([id, item]) => {
            const itemCard = createItemCard(id, item, false);
            itemGrid.appendChild(itemCard);
        });
    }

    // Create item card element
    function createItemCard(id, item, isPopular = false) {
        const card = document.createElement('div');
        card.className = `item-card ${isPopular ? 'popular-item' : ''}`;
        card.innerHTML = `
            <div class="item-icon">${item.icon}</div>
            <div class="item-info">
                <div class="item-name">${item.name}</div>
                <div class="item-category">${item.category}</div>
                <div class="item-id">ID: ${id}</div>
            </div>
            <button type="button" class="add-item-btn" onclick="addItem(${id})">
                <i class="fas fa-plus"></i>
            </button>
        `;
        return card;
    }

    // Add item to selection
    window.addItem = function(itemId) {
        const item = allItems[itemId];
        if (!item) return;

        // Check if item already selected
        const existingIndex = selectedItems.findIndex(selected => selected.id == itemId);
        if (existingIndex !== -1) {
            // Increase quantity
            selectedItems[existingIndex].quantity += 1;
        } else {
            // Add new item
            selectedItems.push({
                id: itemId,
                name: item.name,
                icon: item.icon,
                category: item.category,
                quantity: 1
            });
        }

        updateSelectedItemsDisplay();
        updateHiddenTextarea();
    };

    // Remove item from selection
    window.removeItem = function(itemId) {
        selectedItems = selectedItems.filter(item => item.id != itemId);
        updateSelectedItemsDisplay();
        updateHiddenTextarea();
    };

    // Update quantity
    window.updateQuantity = function(itemId, quantity) {
        const index = selectedItems.findIndex(item => item.id == itemId);
        if (index !== -1) {
            selectedItems[index].quantity = Math.max(1, parseInt(quantity) || 1);
            updateSelectedItemsDisplay();
            updateHiddenTextarea();
        }
    };

    // Update selected items display
    function updateSelectedItemsDisplay() {
        const container = document.getElementById('selected_items_list');

        if (selectedItems.length === 0) {
            container.innerHTML = `
                <div class="empty-selection">
                    <i class="fas fa-inbox"></i>
                    <p>Chưa có item nào được chọn</p>
                </div>
            `;
            return;
        }

        container.innerHTML = selectedItems.map(item => `
            <div class="selected-item">
                <div class="selected-item-icon">${item.icon}</div>
                <div class="selected-item-info">
                    <div class="selected-item-name">${item.name}</div>
                    <div class="selected-item-category">${item.category}</div>
                    <div class="selected-item-id">ID: ${item.id}</div>
                </div>
                <div class="selected-item-quantity">
                    <input type="number"
                           value="${item.quantity}"
                           min="1"
                           max="999"
                           onchange="updateQuantity(${item.id}, this.value)"
                           class="quantity-input">
                </div>
                <button type="button"
                        class="remove-item-btn"
                        onclick="removeItem(${item.id})"
                        title="Xóa item">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `).join('');
    }

    // Update hidden textarea for form submission
    function updateHiddenTextarea() {
        const textarea = document.getElementById('reward_items');
        const itemLines = selectedItems.map(item =>
            `${item.id},${item.quantity},${item.name}`
        );
        textarea.value = itemLines.join('\n');
    }

    // Search functionality
    document.getElementById('item_search').addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase();
        filterItems(searchTerm, document.getElementById('item_category').value);
    });

    // Handle restriction type change
    const restrictionRadios = document.querySelectorAll('input[name="restriction_type"]');
    const specificUsersSection = document.getElementById('specific_users_section');
    const allowedUsernamesTextarea = document.getElementById('allowed_usernames');

    function toggleRestrictionSection() {
        const isSpecific = document.getElementById('restriction_specific_users').checked;
        specificUsersSection.style.display = isSpecific ? 'block' : 'none';
        allowedUsernamesTextarea.required = isSpecific;
    }

    restrictionRadios.forEach(radio => {
        radio.addEventListener('change', toggleRestrictionSection);
    });

    // Initial check on page load to set correct state
    toggleRestrictionSection();

    // Category filter
    document.getElementById('item_category').addEventListener('change', function(e) {
        const category = e.target.value;
        filterItems(document.getElementById('item_search').value.toLowerCase(), category);
    });

    // Filter items
    function filterItems(searchTerm, category) {
        const itemGrid = document.getElementById('item_grid');
        itemGrid.innerHTML = '';

        Object.entries(allItems).forEach(([id, item]) => {
            const matchesSearch = !searchTerm ||
                item.name.toLowerCase().includes(searchTerm) ||
                item.category.toLowerCase().includes(searchTerm);

            const matchesCategory = !category || item.category === category;

            if (matchesSearch && matchesCategory) {
                const itemCard = createItemCard(id, item, false);
                itemGrid.appendChild(itemCard);
            }
        });
    }

    // Initialize item loading
    loadItems();
});
</script>

<style>
/* Modern Giftcode Creation Styles */
.giftcode-create-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

/* Page Header */
.page-header-modern {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 25px;
    background: linear-gradient(135deg, rgba(147, 51, 234, 0.1) 0%, rgba(79, 70, 229, 0.1) 100%);
    border: 1px solid rgba(147, 51, 234, 0.2);
    border-radius: 20px;
    backdrop-filter: blur(20px);
}

.header-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #9333ea 0%, #4f46e5 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    box-shadow: 0 8px 25px rgba(147, 51, 234, 0.3);
}

.header-text h1 {
    margin: 0;
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, #9333ea 0%, #4f46e5 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-text p {
    margin: 5px 0 0 0;
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
}

.header-actions .btn-glass {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 12px 20px;
    border-radius: 12px;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.header-actions .btn-glass:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Form Cards */
.form-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    margin-bottom: 25px;
    overflow: hidden;
    backdrop-filter: blur(20px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.card-header-modern {
    background: linear-gradient(135deg, rgba(147, 51, 234, 0.2) 0%, rgba(79, 70, 229, 0.2) 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px 25px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.card-icon {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #9333ea 0%, #4f46e5 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: white;
    box-shadow: 0 4px 15px rgba(147, 51, 234, 0.3);
}

.card-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: white;
}

.card-body-modern {
    padding: 25px;
}

/* Form Groups */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-group-modern {
    margin-bottom: 20px;
}

.form-group-modern.full-width {
    grid-column: 1 / -1;
}

.form-label-modern {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
    font-weight: 600;
    color: white;
    font-size: 14px;
}

.form-label-modern i {
    color: #9333ea;
    width: 16px;
}

.required {
    color: #ef4444;
    font-weight: bold;
}

/* Form Inputs */
.form-input-modern, .form-textarea-modern {
    width: 100%;
    padding: 15px 18px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.05);
    color: white;
    font-size: 14px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.form-input-modern:focus, .form-textarea-modern:focus {
    outline: none;
    border-color: #9333ea;
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 0 4px rgba(147, 51, 234, 0.1);
}

.form-input-modern::placeholder, .form-textarea-modern::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.coin-input {
    background: linear-gradient(135deg, rgba(251, 191, 36, 0.1) 0%, rgba(245, 158, 11, 0.1) 100%);
    border-color: rgba(251, 191, 36, 0.3);
}

.item-textarea {
    font-family: 'Courier New', monospace;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
    border-color: rgba(34, 197, 94, 0.3);
}

.form-hint {
    display: block;
    margin-top: 8px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    font-style: italic;
}

.error-message {
    margin-top: 8px;
    color: #ef4444;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.error-message::before {
    content: "⚠️";
    font-size: 14px;
}

/* Radio Groups */
.radio-group-modern {
    margin-bottom: 25px;
}

.radio-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.radio-option {
    position: relative;
}

.radio-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.radio-label:hover {
    border-color: rgba(147, 51, 234, 0.5);
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.radio-option input[type="radio"]:checked + .radio-label {
    border-color: #9333ea;
    background: linear-gradient(135deg, rgba(147, 51, 234, 0.2) 0%, rgba(79, 70, 229, 0.2) 100%);
    box-shadow: 0 8px 25px rgba(147, 51, 234, 0.2);
}

.radio-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    flex-shrink: 0;
}

.radio-icon.coin-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.radio-icon.item-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.radio-icon.mixed-icon {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.radio-content {
    flex: 1;
}

.radio-title {
    display: block;
    font-weight: 600;
    color: white;
    font-size: 16px;
    margin-bottom: 4px;
}

.radio-desc {
    display: block;
    color: rgba(255, 255, 255, 0.6);
    font-size: 13px;
}

/* Code and Reward Sections */
.code-section, .reward-section {
    margin-top: 20px;
    transition: all 0.3s ease;
}

/* Item Format Guide */
.item-format-guide {
    margin-top: 15px;
    padding: 15px;
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.guide-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #60a5fa;
    margin-bottom: 8px;
    font-size: 14px;
}

.guide-content {
    color: rgba(255, 255, 255, 0.8);
    font-size: 13px;
    line-height: 1.5;
}

.guide-content code {
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    color: #60a5fa;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
    padding: 25px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
}

.btn-primary-modern {
    background: linear-gradient(135deg, #9333ea 0%, #4f46e5 100%);
    border: none;
    color: white;
    padding: 15px 30px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(147, 51, 234, 0.3);
}

.btn-primary-modern:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(147, 51, 234, 0.4);
}

.btn-primary-modern:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.btn-secondary-modern {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 13px 28px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-secondary-modern:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .giftcode-create-container {
        padding: 15px;
    }

    .page-header-modern {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .radio-options {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }

    .btn-primary-modern, .btn-secondary-modern {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .header-text h1 {
        font-size: 24px;
    }

    .card-body-modern {
        padding: 20px;
    }

    .radio-label {
        padding: 15px;
    }

    .radio-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
}

/* Item Selection Styles */
.item-selection-container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.item-selection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.selection-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
    display: flex;
    align-items: center;
    gap: 10px;
}

.selection-controls {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.search-input, .category-select {
    min-width: 200px;
    padding: 10px 15px;
    font-size: 14px;
}

.popular-items-section {
    margin-bottom: 30px;
}

.popular-title {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    color: #fbbf24;
    display: flex;
    align-items: center;
    gap: 8px;
}

.popular-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.item-browser {
    margin-bottom: 30px;
}

.item-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
    padding: 15px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.item-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
    backdrop-filter: blur(10px);
}

.item-card:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: #9333ea;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(147, 51, 234, 0.2);
}

.item-card.popular-item {
    border-color: #fbbf24;
    background: linear-gradient(135deg, rgba(251, 191, 36, 0.1) 0%, rgba(245, 158, 11, 0.1) 100%);
}

.item-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    flex-shrink: 0;
}

.item-info {
    flex: 1;
    min-width: 0;
}

.item-name {
    font-weight: 600;
    color: white;
    font-size: 14px;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.item-category {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 2px;
}

.item-id {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.4);
    font-family: 'Courier New', monospace;
}

.add-item-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.add-item-btn:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: scale(1.1);
}

.selected-items-section {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 25px;
}

.selected-title {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    color: #10b981;
    display: flex;
    align-items: center;
    gap: 8px;
}

.selected-items-list {
    background: rgba(255, 255, 255, 0.02);
    border-radius: 10px;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    min-height: 100px;
}

.empty-selection {
    text-align: center;
    color: rgba(255, 255, 255, 0.5);
    padding: 30px;
}

.empty-selection i {
    font-size: 32px;
    margin-bottom: 10px;
    display: block;
}

.selected-item {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.selected-item:last-child {
    margin-bottom: 0;
}

.selected-item:hover {
    background: rgba(255, 255, 255, 0.15);
}

.selected-item-icon {
    font-size: 20px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    flex-shrink: 0;
}

.selected-item-info {
    flex: 1;
    min-width: 0;
}

.selected-item-name {
    font-weight: 600;
    color: white;
    font-size: 14px;
    margin-bottom: 2px;
}

.selected-item-category {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 1px;
}

.selected-item-id {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.4);
    font-family: 'Courier New', monospace;
}

.selected-item-quantity {
    margin-right: 10px;
}

.quantity-input {
    width: 60px;
    padding: 6px 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    text-align: center;
    font-size: 14px;
}

.quantity-input:focus {
    outline: none;
    border-color: #9333ea;
    background: rgba(255, 255, 255, 0.15);
}

.remove-item-btn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border: none;
    color: white;
    width: 28px;
    height: 28px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.remove-item-btn:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: scale(1.1);
}
</style>
@endsection
