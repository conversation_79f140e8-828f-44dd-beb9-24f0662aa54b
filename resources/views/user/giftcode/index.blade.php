@extends('layouts.user')

@section('title', __('user.giftcode_title'))

@section('content')
<!-- Giftcode Input -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-gift"></i>
            {{ __('user.giftcode') }}
        </h3>
    </div>
    <div style="margin-bottom: 1.5rem;">
        <div style="background: #f0f9ff; border: 1px solid #bae6fd; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem;">
            <div style="font-weight: 500; color: #0369a1; margin-bottom: 0.5rem;">
                <i class="fas fa-info-circle"></i> {{ __('user.giftcode_guide') }}
            </div>
            <ul style="color: #6b7280; font-size: 0.875rem; margin: 0; padding-left: 1rem;">
                <li>{{ __('user.giftcode_rule_1') }}</li>
                <li>{{ __('user.giftcode_rule_2') }}</li>
                <li>{{ __('user.giftcode_rule_3') }}</li>
                <li>{{ __('user.giftcode_rule_4') }}</li>
            </ul>
        </div>
    </div>

    @if($user)
        <form method="POST" action="{{ route('user.giftcode.redeem') }}" id="giftcodeForm">
            @csrf

            <div class="form-group">
                <label for="character_id" class="form-label">{{ __('user.select_character_reward') }}</label>
                <select name="character_id" id="character_id" class="form-input" required>
                    <option value="">{{ __('user.select_character_placeholder') }}</option>
                    @if(isset($characters) && $characters->count() > 0)
                        @foreach($characters as $character)
                            <option value="{{ $character->rid }}">{{ $character->rname }} ({{ __('user.level') }} {{ $character->level }})</option>
                        @endforeach
                    @else
                        <option value="">{{ __('user.no_characters') }}</option>
                    @endif
                </select>
                <div style="color: #6b7280; font-size: 0.75rem; margin-top: 0.25rem;">
                    {{ __('user.reward_sent_to_character') }}
                </div>
            </div>

            <div class="form-group">
                <label for="giftcode" class="form-label">{{ __('user.giftcode_code') }}</label>
                <div style="display: flex; gap: 1rem;">
                    <input
                        type="text"
                        name="giftcode"
                        id="giftcode"
                        class="form-input"
                        placeholder="{{ __('user.enter_giftcode_placeholder') }}"
                        required
                        style="flex: 1; text-transform: uppercase;"
                        maxlength="20"
                    >
                    <button type="submit" class="btn btn-primary" style="min-width: 120px;" id="submitBtn">
                        <i class="fas fa-gift"></i>
                        {{ __('user.enter_giftcode_code') }}
                    </button>
                </div>
                <div style="color: #6b7280; font-size: 0.75rem; margin-top: 0.25rem;">
                    {{ __('user.giftcode_length_note') }}
                </div>
            </div>
        </form>
    @else
        <div style="text-align: center; color: #6b7280; padding: 2rem;">
            <i class="fas fa-unlink" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
            <p>{{ __('user.need_link_account') }}</p>
            <a href="{{ route('user.profile') }}" class="btn btn-primary" style="margin-top: 1rem;">
                <i class="fas fa-link"></i> {{ __('user.link_now') }}
            </a>
        </div>
    @endif
</div>

<!-- Active Giftcodes -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-tags"></i>
            {{ __('user.active_giftcodes') }}
        </h3>
    </div>
    <div id="activeGiftcodes">
        <div style="text-align: center; color: #6b7280; padding: 2rem;">
            <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 1rem;"></i>
            <p>{{ __('user.loading_giftcodes') }}</p>
        </div>
    </div>
</div>

<!-- Giftcode History -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-history"></i>
            {{ __('user.giftcode_usage_history') }}
        </h3>
    </div>
    @if($user)
        <div id="giftcodeHistory">
            <div style="text-align: center; color: #6b7280; padding: 2rem;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                <p>{{ __('user.loading_history') }}</p>
            </div>
        </div>
    @else
        <div style="text-align: center; color: #6b7280; padding: 2rem;">
            <i class="fas fa-lock" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
            <p>{{ __('user.need_link_to_view_history') }}</p>
        </div>
    @endif
</div>

<!-- Giftcode Types Info -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-question-circle"></i>
            {{ __('user.giftcode_types') }}
        </h3>
    </div>
    <div class="grid grid-3">
        <div style="text-align: center; padding: 1.5rem; background: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 8px;">
            <i class="fas fa-calendar-day" style="font-size: 2rem; color: #10b981; margin-bottom: 1rem;"></i>
            <h4 style="margin-bottom: 0.5rem; color: #166534;">{{ __('user.event_code') }}</h4>
            <p style="color: #6b7280; font-size: 0.875rem;">{{ __('user.event_code_desc') }}</p>
        </div>
        <div style="text-align: center; padding: 1.5rem; background: #fef3c7; border: 1px solid #fed7aa; border-radius: 8px;">
            <i class="fas fa-star" style="font-size: 2rem; color: #f59e0b; margin-bottom: 1rem;"></i>
            <h4 style="margin-bottom: 0.5rem; color: #92400e;">{{ __('user.vip_code') }}</h4>
            <p style="color: #6b7280; font-size: 0.875rem;">{{ __('user.vip_code_desc') }}</p>
        </div>
        <div style="text-align: center; padding: 1.5rem; background: #f0f9ff; border: 1px solid #bae6fd; border-radius: 8px;">
            <i class="fas fa-users" style="font-size: 2rem; color: #3b82f6; margin-bottom: 1rem;"></i>
            <h4 style="margin-bottom: 0.5rem; color: #1e40af;">{{ __('user.public_code') }}</h4>
            <p style="color: #6b7280; font-size: 0.875rem;">{{ __('user.public_code_desc') }}</p>
        </div>
    </div>
</div>

<!-- Tips -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-lightbulb"></i>
            {{ __('user.giftcode_tips') }}
        </h3>
    </div>
    <div class="grid grid-2">
        <div>
            <h4 style="color: #374151; margin-bottom: 1rem;">📱 {{ __('user.follow_new_giftcodes') }}</h4>
            <ul style="color: #6b7280; font-size: 0.875rem; margin: 0; padding-left: 1rem;">
                <li>{{ __('user.follow_facebook_fanpage') }}</li>
                <li>{{ __('user.join_discord_telegram') }}</li>
                <li>{{ __('user.subscribe_email_notifications') }}</li>
                <li>{{ __('user.check_website_regularly') }}</li>
            </ul>
        </div>
        <div>
            <h4 style="color: #374151; margin-bottom: 1rem;">⚡ {{ __('user.use_effectively') }}</h4>
            <ul style="color: #6b7280; font-size: 0.875rem; margin: 0; padding-left: 1rem;">
                <li>{{ __('user.enter_giftcode_immediately') }}</li>
                <li>{{ __('user.check_expiration_date') }}</li>
                <li>{{ __('user.ensure_account_linked') }}</li>
                <li>{{ __('user.note_usage_conditions') }}</li>
            </ul>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load characters first
    loadCharacters();

    // Load active giftcodes
    loadActiveGiftcodes();

    // Load giftcode history
    @if($user)
        loadGiftcodeHistory();
    @endif

    // Handle form submission
    document.getElementById('giftcodeForm').addEventListener('submit', function(e) {
        e.preventDefault();
        redeemGiftcode();
    });

    // Auto uppercase giftcode input
    document.getElementById('giftcode').addEventListener('input', function() {
        this.value = this.value.toUpperCase();
    });
});

// Characters are now loaded directly from server-side, no need for AJAX

function redeemGiftcode() {
    const form = document.getElementById('giftcodeForm');
    const submitBtn = document.getElementById('submitBtn');
    const formData = new FormData(form);

    // Validate
    if (!formData.get('character_id')) {
        alert('Vui lòng chọn nhân vật nhận thưởng');
        return;
    }

    if (!formData.get('giftcode')) {
        alert('Vui lòng nhập mã giftcode');
        return;
    }

    // Disable submit button
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang xử lý...';

    fetch('/user/giftcode/redeem', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            form.reset();
            // Characters don't need reloading as they're static
        } else {
            alert('Lỗi: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi nhập giftcode');
    })
    .finally(() => {
        // Re-enable submit button
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-gift"></i> Nhập code';
    });
}

// Load active giftcodes from API
function loadActiveGiftcodes() {
    fetch('/user/giftcode/active')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayGiftcodes(data.giftcodes || data.data);
            } else {
                console.error('Failed to load giftcodes:', data.message);
            }
        })
        .catch(error => {
            console.error('Error loading giftcodes:', error);
        });
}

function displayGiftcodes(giftcodes) {
    const container = document.getElementById('activeGiftcodes');

    if (!giftcodes || giftcodes.length === 0) {
        container.innerHTML = `
            <div style="text-align: center; color: #6b7280; padding: 2rem;">
                <i class="fas fa-inbox" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <p>Hiện tại không có giftcode nào đang hoạt động</p>
            </div>
        `;
        return;
    }

    let html = '<div style="display: flex; flex-direction: column; gap: 1rem;">';

    giftcodes.forEach(gift => {
        const typeColors = {
            1: { bg: '#f0f9ff', border: '#bae6fd', text: '#1e40af', name: 'Công khai' }, // Public
            2: { bg: '#fef3c7', border: '#fed7aa', text: '#92400e', name: 'Riêng tư' }, // Private
            0: { bg: '#f0fdf4', border: '#bbf7d0', text: '#166534', name: 'Theo NV' }  // Character
        };

        const colors = typeColors[gift.type] || typeColors[1];
        const progress = gift.limit > 0 ? (gift.used_count / gift.limit) * 100 : 0;

        // Calculate expiry date
        let expiryText = 'Không giới hạn';
        if (gift.period > 0) {
            const createdDate = new Date(gift.created_at);
            const expiryDate = new Date(createdDate.getTime() + (gift.period * 24 * 60 * 60 * 1000));
            expiryText = expiryDate.toLocaleDateString('vi-VN');
        }

        html += `
            <div style="background: ${colors.bg}; border: 1px solid ${colors.border}; border-radius: 12px; padding: 1.5rem;">
                <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 1rem;">
                    <div>
                        <div style="font-size: 1.25rem; font-weight: 600; color: ${colors.text}; margin-bottom: 0.5rem;">
                            Giftcode ${colors.name}
                        </div>
                        <div style="color: #6b7280; font-size: 0.875rem; margin-bottom: 0.5rem;">
                            ${gift.name || gift.content || 'Giftcode đặc biệt'}
                        </div>
                        <div style="color: #6b7280; font-size: 0.75rem;">
                            Hết hạn: ${expiryText}
                        </div>
                    </div>
                    <div style="text-align: right;">
                        <div style="background: rgba(255,255,255,0.8); padding: 0.5rem; border-radius: 6px; font-family: monospace; font-weight: 600; color: ${colors.text}; margin-bottom: 0.5rem;">
                            Nhập code để nhận thưởng
                        </div>
                        <div style="font-size: 0.75rem; color: #6b7280;">
                            Loại: ${colors.name}
                        </div>
                    </div>
                </div>

                <div style="margin-bottom: 1rem;">
                    <div style="font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Mô tả:</div>
                    <div style="color: #6b7280; font-size: 0.875rem;">
                        ${gift.content || gift.description || 'Phần thưởng đặc biệt từ hệ thống giftcode!'}
                    </div>
                </div>

                <div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.25rem;">
                        <span style="font-size: 0.75rem; color: #6b7280;">Đã sử dụng:</span>
                        <span style="font-size: 0.75rem; color: #6b7280;">${gift.used_count}/${gift.remaining_uses}</span>
                    </div>
                    <div style="background: rgba(255,255,255,0.5); border-radius: 9999px; height: 0.5rem;">
                        <div style="background: ${colors.text}; height: 100%; border-radius: 9999px; width: ${progress}%;"></div>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;
}

// Load giftcode history
function loadGiftcodeHistory() {
    @if($user)
    fetch('/user/giftcode/history')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('giftcodeHistory');

            if (!data.success || !data.data || data.data.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; color: #6b7280; padding: 2rem;">
                        <i class="fas fa-inbox" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <p>Chưa sử dụng giftcode nào</p>
                    </div>
                `;
                return;
            }

            let html = '<div style="overflow-x: auto;"><table style="width: 100%; border-collapse: collapse;">';
            html += `
                <thead>
                    <tr style="border-bottom: 1px solid #e5e7eb;">
                        <th style="text-align: left; padding: 0.75rem; font-weight: 500; color: #374151;">Mã code</th>
                        <th style="text-align: left; padding: 0.75rem; font-weight: 500; color: #374151;">Nhân vật</th>
                        <th style="text-align: left; padding: 0.75rem; font-weight: 500; color: #374151;">Thời gian</th>
                        <th style="text-align: left; padding: 0.75rem; font-weight: 500; color: #374151;">Trạng thái</th>
                    </tr>
                </thead>
                <tbody>
            `;

            data.data.forEach(item => {
                html += `
                    <tr style="border-bottom: 1px solid #f3f4f6;">
                        <td style="padding: 0.75rem; font-family: monospace; font-weight: 500;">${item.giftcode}</td>
                        <td style="padding: 0.75rem; color: #6b7280;">
                            ${item.character_name || 'N/A'}
                        </td>
                        <td style="padding: 0.75rem; color: #6b7280; font-size: 0.875rem;">
                            ${new Date(item.created_at).toLocaleString('vi-VN')}
                        </td>
                        <td style="padding: 0.75rem;">
                            <span style="background: #dcfce7; color: #166534; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem; font-weight: 500;">
                                Thành công
                            </span>
                        </td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';
            container.innerHTML = html;
        })
        .catch(error => {
            console.error('Error loading giftcode history:', error);
            document.getElementById('giftcodeHistory').innerHTML = `
                <div style="text-align: center; color: #ef4444; padding: 2rem;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                    <p>Lỗi tải lịch sử sử dụng</p>
                </div>
            `;
        });
    @endif
}

// Copy giftcode function
function copyGiftcode(code) {
    navigator.clipboard.writeText(code).then(function() {
        // Show temporary success message
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
        btn.style.background = '#10b981';
        btn.style.color = 'white';

        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.style.background = '';
            btn.style.color = '';
        }, 2000);
    });
}

// Form submission - use the existing handler that calls /user/giftcode/redeem
// The existing handler at line 243 is already correct and will use the updated controller

// Load data when page loads
document.addEventListener('DOMContentLoaded', function() {
    loadActiveGiftcodes();
    loadGiftcodeHistory();
});
@endsection
