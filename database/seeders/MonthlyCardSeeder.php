<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class MonthlyCardSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Sample monthly card purchases for testing
        $purchases = [
            [
                'user_id' => 1, // Assuming user ID 1 exists
                'package_name' => 'Thẻ Th<PERSON>g <PERSON>',
                'package_type' => 'basic',
                'duration_days' => 30,
                'cost_coins' => 50000,
                'daily_reward_coins' => 2000,
                'bonus_items' => json_encode([]),
                'daily_items' => json_encode([]),
                'status' => 'active',
                'activated_at' => Carbon::now()->subDays(5),
                'expires_at' => Carbon::now()->addDays(25),
                'last_claimed_at' => Carbon::now()->subDay(),
                'days_claimed' => 5,
                'notes' => 'Test monthly card purchase',
                'ip_address' => '127.0.0.1',
                'created_at' => Carbon::now()->subDays(5),
                'updated_at' => Carbon::now()->subDay()
            ],
            [
                'user_id' => 1,
                'package_name' => 'Thẻ <PERSON><PERSON><PERSON><PERSON>',
                'package_type' => 'premium',
                'duration_days' => 30,
                'cost_coins' => 100000,
                'daily_reward_coins' => 4000,
                'bonus_items' => json_encode(['14,10,1,0,0,0,0']),
                'daily_items' => json_encode(['14,1,1,0,0,0,0']),
                'status' => 'expired',
                'activated_at' => Carbon::now()->subDays(45),
                'expires_at' => Carbon::now()->subDays(15),
                'last_claimed_at' => Carbon::now()->subDays(16),
                'days_claimed' => 29,
                'notes' => 'Expired premium card',
                'ip_address' => '127.0.0.1',
                'created_at' => Carbon::now()->subDays(45),
                'updated_at' => Carbon::now()->subDays(15)
            ]
        ];

        foreach ($purchases as $purchase) {
            DB::table('monthly_card_purchases')->insert($purchase);
        }

        // Sample coin spend logs for monthly card purchases
        $spendLogs = [
            [
                'account_id' => 1,
                'username' => 'testuser',
                'transaction_id' => 'MONTHLY_CARD_' . time() . '_1001',
                'coins_spent' => 50000,
                'item_type' => 'monthly_card',
                'item_name' => 'Thẻ Tháng Cơ Bản',
                'item_data' => json_encode([
                    'package_type' => 'basic',
                    'duration_days' => 30,
                    'purchase_id' => 1
                ]),
                'description' => 'Mua thẻ tháng Thẻ Tháng Cơ Bản',
                'ip_address' => '127.0.0.1',
                'created_at' => Carbon::now()->subDays(5),
                'updated_at' => Carbon::now()->subDays(5)
            ],
            [
                'account_id' => 1,
                'username' => 'testuser',
                'transaction_id' => 'MONTHLY_CARD_' . time() . '_1002',
                'coins_spent' => 100000,
                'item_type' => 'monthly_card',
                'item_name' => 'Thẻ Tháng Cao Cấp',
                'item_data' => json_encode([
                    'package_type' => 'premium',
                    'duration_days' => 30,
                    'purchase_id' => 2
                ]),
                'description' => 'Mua thẻ tháng Thẻ Tháng Cao Cấp',
                'ip_address' => '127.0.0.1',
                'created_at' => Carbon::now()->subDays(45),
                'updated_at' => Carbon::now()->subDays(45)
            ]
        ];

        foreach ($spendLogs as $log) {
            DB::table('coin_spend_logs')->insert($log);
        }
    }
}
