<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class GiftcodeUsageLogSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Sample giftcode usage logs for testing
        $logs = [
            [
                'user_id' => 12, // Assuming user ID 12 exists
                'giftcode' => 'WELCOME2025',
                'giftcode_name' => 'Giftcode chào mừng năm mới',
                'rewards' => json_encode(['1000 Coin', '5x Jewel of Bless', '10x Jewel of Soul']),
                'status' => 'success',
                'failure_reason' => null,
                'failure_type' => null,
                'giftcode_id' => 1,
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'created_at' => Carbon::now()->subDays(2),
                'updated_at' => Carbon::now()->subDays(2)
            ],
            [
                'user_id' => 12,
                'giftcode' => 'TESTCODE',
                'giftcode_name' => 'Test Giftcode',
                'rewards' => json_encode(['500 Coin']),
                'status' => 'success',
                'failure_reason' => null,
                'failure_type' => null,
                'giftcode_id' => 4,
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'created_at' => Carbon::now()->subDays(5),
                'updated_at' => Carbon::now()->subDays(5)
            ],
            [
                'user_id' => 12,
                'giftcode' => 'EXPIRED123',
                'giftcode_name' => null,
                'rewards' => null,
                'status' => 'failed',
                'failure_reason' => 'Giftcode đã hết hạn',
                'failure_type' => 'expired',
                'giftcode_id' => 5,
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'created_at' => Carbon::now()->subDays(1),
                'updated_at' => Carbon::now()->subDays(1)
            ],
            [
                'user_id' => 12,
                'giftcode' => 'INVALIDCODE',
                'giftcode_name' => null,
                'rewards' => null,
                'status' => 'failed',
                'failure_reason' => 'Giftcode không tồn tại',
                'failure_type' => 'invalid',
                'giftcode_id' => null,
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'created_at' => Carbon::now()->subHours(6),
                'updated_at' => Carbon::now()->subHours(6)
            ],
            [
                'user_id' => 12,
                'giftcode' => 'WELCOME2025',
                'giftcode_name' => null,
                'rewards' => null,
                'status' => 'failed',
                'failure_reason' => 'Bạn đã sử dụng giftcode này rồi',
                'failure_type' => 'used',
                'giftcode_id' => 1,
                'ip_address' => '127.0.0.1',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'created_at' => Carbon::now()->subHours(2),
                'updated_at' => Carbon::now()->subHours(2)
            ]
        ];

        foreach ($logs as $log) {
            DB::table('giftcode_usage_logs')->insert($log);
        }
    }
}
