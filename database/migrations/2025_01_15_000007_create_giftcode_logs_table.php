<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateGiftcodeLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('giftcode_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('uid'); // User ID from t_account
            $table->unsignedBigInteger('rid')->nullable(); // Character ID from t_roles
            $table->integer('zoneid')->default(1); // Server/Zone ID
            $table->string('giftcode'); // The actual gift code used
            $table->unsignedBigInteger('groupid'); // Reference to giftcodes table
            $table->string('character_name')->nullable(); // Character name for easy reference
            $table->string('username')->nullable(); // Username for easy reference
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['uid', 'groupid']);
            $table->index(['rid', 'groupid']);
            $table->index(['giftcode']);
            $table->index(['zoneid']);
            $table->index(['created_at']);

            // Foreign key
            $table->foreign('groupid')->references('id')->on('giftcodes')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('giftcode_logs');
    }
}
