<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateGameServerGiftcodeTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Create z_giftcode table on game server database
        Schema::connection('game_mysql')->create('z_giftcode', function (Blueprint $table) {
            $table->id();
            $table->string('cc')->nullable(); // Unknown field
            $table->string('code'); // Giftcode string
            $table->tinyInteger('mail')->default(1); // Mail type (corresponds to giftcode type)
            $table->integer('count')->default(0); // Usage count
            $table->integer('maxcount')->default(1); // Max usage count
            $table->string('userid')->nullable(); // User ID allowed to use (for private codes)
            $table->text('itemlist'); // Items list: goodsid,gcount,binding,forge_level,appendproplev,lucky,excellenceinfo
            $table->timestamps();
            
            // Indexes
            $table->unique('code');
            $table->index('mail');
            $table->index('userid');
        });
        
        // Create z_giftcoderecord table on game server database
        Schema::connection('game_mysql')->create('z_giftcoderecord', function (Blueprint $table) {
            $table->id();
            $table->string('userid'); // User ID who used the code
            $table->string('code'); // Giftcode used
            $table->integer('zoneid')->default(1); // Server/Zone ID
            $table->timestamp('created_at')->useCurrent();
            
            // Indexes
            $table->index(['userid', 'code']);
            $table->index('zoneid');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('game_mysql')->dropIfExists('z_giftcoderecord');
        Schema::connection('game_mysql')->dropIfExists('z_giftcode');
    }
}
