<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Http\Controllers\User\MonthlyCardController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\DB;

echo "=== Test Hoàn chỉnh Hệ thống Monthly Card ===\n\n";

try {
    // Test 1: Kiểm tra controller và config
    echo "1. Test MonthlyCardController...\n";

    // Mock GameCacheService
    $gameCacheService = app(\App\Services\GameCacheService::class);
    $controller = new MonthlyCardController($gameCacheService);
    
    // Use reflection to access private method
    $reflection = new ReflectionClass($controller);
    $method = $reflection->getMethod('getMonthlyCardPackages');
    $method->setAccessible(true);
    $packages = $method->invoke($controller);
    
    $package = $packages['premium'];
    echo "✓ Package loaded from controller\n";
    echo "  - Name: {$package['name']}\n";
    echo "  - Cost: " . number_format($package['cost_coins']) . " coins\n";
    echo "  - RMB: {$package['rmb_amount']} RMB\n";
    echo "  - Daily Reward: " . number_format($package['daily_reward_coins']) . " coins\n";
    
    // Verify price is correct
    if ($package['cost_coins'] == 1500000) {
        echo "✓ Giá tiền đã được cập nhật đúng: 1,500,000 coins\n";
    } else {
        echo "✗ Giá tiền chưa đúng: " . number_format($package['cost_coins']) . " (expected: 1,500,000)\n";
    }
    
    echo "\n";

    // Test 2: Test database structure
    echo "2. Test cấu trúc database...\n";
    
    // Check t_history table
    $historyColumns = DB::select('DESCRIBE t_history');
    $hasRequiredColumns = false;
    foreach ($historyColumns as $column) {
        if ($column->Field == 'type' || $column->Field == 'balance' || $column->Field == 'content') {
            $hasRequiredColumns = true;
            break;
        }
    }
    if ($hasRequiredColumns) {
        echo "✓ Bảng t_history có cấu trúc đúng\n";
    } else {
        echo "✗ Bảng t_history thiếu columns cần thiết\n";
    }
    
    // Check t_tempmoney table
    $tempMoneyColumns = DB::connection('game_mysql')->select('DESCRIBE t_tempmoney');
    $tempMoneyOK = false;
    foreach ($tempMoneyColumns as $column) {
        if ($column->Field == 'uid' || $column->Field == 'addmoney') {
            $tempMoneyOK = true;
            break;
        }
    }
    if ($tempMoneyOK) {
        echo "✓ Bảng t_tempmoney có cấu trúc đúng\n";
    } else {
        echo "✗ Bảng t_tempmoney thiếu columns cần thiết\n";
    }
    
    // Check monthly_card_purchases table
    $purchaseColumns = DB::select('DESCRIBE monthly_card_purchases');
    $purchaseOK = false;
    foreach ($purchaseColumns as $column) {
        if ($column->Field == 'cost_coins' || $column->Field == 'daily_reward_coins') {
            $purchaseOK = true;
            break;
        }
    }
    if ($purchaseOK) {
        echo "✓ Bảng monthly_card_purchases có cấu trúc đúng\n";
    } else {
        echo "✗ Bảng monthly_card_purchases thiếu columns cần thiết\n";
    }
    
    echo "\n";

    // Test 3: Test flow hoàn chỉnh với dữ liệu thực
    echo "3. Test flow mua thẻ tháng hoàn chỉnh...\n";
    
    // Get existing test user
    $testUser = DB::table('t_account')->where('UserName', 'testcard')->first();
    if (!$testUser) {
        echo "✗ Không tìm thấy test user\n";
        return;
    }
    
    // Get test character
    $gameUserId = 'ZT' . str_pad($testUser->ID, 4, '0', STR_PAD_LEFT);
    $testCharacter = DB::connection('game_mysql')->table('t_roles')
        ->where('userid', $gameUserId)
        ->where('isdel', 0)
        ->first();
    
    if (!$testCharacter) {
        echo "✗ Không tìm thấy test character\n";
        return;
    }
    
    echo "✓ Test user và character sẵn sàng\n";
    echo "  - User: {$testUser->UserName} (ID: {$testUser->ID})\n";
    echo "  - Character: {$testCharacter->rname} (ID: {$testCharacter->rid})\n";
    
    // Check current balance
    $userCoins = DB::table('user_coins')->where('account_id', $testUser->ID)->first();
    if ($userCoins && $userCoins->coins >= $package['cost_coins']) {
        echo "✓ User có đủ coin: " . number_format($userCoins->coins) . "\n";
    } else {
        // Add coins for test
        DB::table('user_coins')
            ->where('account_id', $testUser->ID)
            ->update(['coins' => 2000000]);
        echo "✓ Đã cập nhật coin cho test user\n";
    }
    
    echo "\n";

    // Test 4: Simulate purchase request
    echo "4. Test API purchase...\n";
    
    // Create mock request
    $request = new Request();
    $request->merge([
        'package_type' => 'premium',
        'character_id' => $testCharacter->rid
    ]);
    
    // Mock session
    Session::put('user_account', ['id' => $testUser->ID]);
    
    try {
        $response = $controller->purchase($request);
        $responseData = $response->getData(true);
        
        if ($responseData['success']) {
            echo "✓ Purchase API thành công\n";
            echo "  - Message: {$responseData['message']}\n";
            if (isset($responseData['data'])) {
                echo "  - Package: {$responseData['data']['package_name']}\n";
                echo "  - Character: {$responseData['data']['character_name']}\n";
                echo "  - Expires: {$responseData['data']['expires_at']}\n";
            }
        } else {
            echo "✗ Purchase API thất bại: {$responseData['message']}\n";
        }
    } catch (Exception $e) {
        echo "✗ Purchase API error: " . $e->getMessage() . "\n";
    }
    
    echo "\n";

    // Test 5: Verify all database updates
    echo "5. Kiểm tra cập nhật database...\n";
    
    // Check user_coins deduction
    $finalCoins = DB::table('user_coins')->where('account_id', $testUser->ID)->first();
    if ($finalCoins && $finalCoins->total_spent >= $package['cost_coins']) {
        echo "✓ Coin đã được trừ đúng\n";
        echo "  - Total spent: " . number_format($finalCoins->total_spent) . "\n";
    } else {
        echo "✗ Coin chưa được trừ đúng\n";
    }
    
    // Check monthly_card_purchases
    $purchase = DB::table('monthly_card_purchases')
        ->where('user_id', $testUser->ID)
        ->where('cost_coins', $package['cost_coins'])
        ->latest()
        ->first();
    
    if ($purchase) {
        echo "✓ Monthly card purchase record đã được tạo\n";
        echo "  - Package: {$purchase->package_name}\n";
        echo "  - Cost: " . number_format($purchase->cost_coins) . " coins\n";
        echo "  - Status: {$purchase->status}\n";
    } else {
        echo "✗ Monthly card purchase record chưa được tạo\n";
    }
    
    // Check t_tempmoney
    $tempMoney = DB::connection('game_mysql')->table('t_tempmoney')
        ->where('uid', $gameUserId)
        ->where('addmoney', $package['rmb_amount'])
        ->latest('id')
        ->first();
    
    if ($tempMoney) {
        echo "✓ t_tempmoney record đã được tạo\n";
        echo "  - UID: {$tempMoney->uid}\n";
        echo "  - Add Money: {$tempMoney->addmoney} RMB\n";
    } else {
        echo "✗ t_tempmoney record chưa được tạo\n";
    }
    
    // Check t_history
    $history = DB::table('t_history')
        ->where('uid', $testUser->ID)
        ->where('type', 4) // Monthly card type
        ->latest()
        ->first();
    
    if ($history) {
        echo "✓ t_history log đã được tạo\n";
        echo "  - Type: {$history->type} (Monthly Card)\n";
        echo "  - Balance: " . number_format($history->balance) . "\n";
        $content = json_decode($history->content, true);
        if ($content && isset($content['money'])) {
            echo "  - Transaction: " . number_format($content['money']) . " coins\n";
        }
    } else {
        echo "✗ t_history log chưa được tạo\n";
    }
    
    echo "\n";

    // Test 6: Summary
    echo "6. Tóm tắt kết quả...\n";
    
    $totalPurchases = DB::table('monthly_card_purchases')->count();
    $totalRevenue = DB::table('monthly_card_purchases')->sum('cost_coins');
    $activeTempMoney = DB::connection('game_mysql')->table('t_tempmoney')->count();
    $totalHistoryLogs = DB::table('t_history')->where('type', 4)->count();
    
    echo "✓ Tổng số lần mua thẻ tháng: $totalPurchases\n";
    echo "✓ Tổng doanh thu: " . number_format($totalRevenue) . " coins\n";
    echo "✓ Số record t_tempmoney: $activeTempMoney\n";
    echo "✓ Số log t_history (monthly card): $totalHistoryLogs\n";
    
    echo "\n=== Test hoàn thành ===\n";
    echo "🎉 Hệ thống Monthly Card đã sẵn sàng với giá 1,500,000 coins!\n";

} catch (Exception $e) {
    echo "✗ Lỗi: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
