<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Services\GiftcodeService;
use Illuminate\Support\Facades\DB;

echo "=== Test Sử dụng Giftcode ===\n\n";

try {
    $giftcodeService = app(App\Services\GiftcodeService::class);
    
    // Test 1: Tạo giftcode để test
    echo "1. Tạo giftcode test...\n";
    
    $data = [
        'type' => 1, // Public
        'accounts' => null,
        'multiple' => false,
        'code' => 'TESTUSE2025',
        'items' => ['14,5,1,0,0,0,0', '15,10,1,0,0,0,0'], // Jewel of Bless x5, Jewel of Soul x10
        'content' => 'Test giftcode sử dụng',
        'limit' => 100,
        'period' => 30,
        'zoneid' => 1
    ];

    $result = $giftcodeService->createGiftcode($data);
    
    if ($result['success']) {
        echo "✓ Tạo giftcode thành công!\n";
        echo "  - Code: " . implode(', ', $result['codes']) . "\n";
    } else {
        echo "✗ Lỗi: " . $result['message'] . "\n";
        exit;
    }
    
    echo "\n";

    // Test 2: Kiểm tra giftcode đã được sync vào game server
    echo "2. Kiểm tra sync game server...\n";
    
    $gameGiftcode = DB::connection('game_mysql')
        ->table('z_giftcode')
        ->where('code', 'TESTUSE2025')
        ->first();
    
    if ($gameGiftcode) {
        echo "✓ Giftcode đã được sync vào game server\n";
        echo "  - Code: {$gameGiftcode->code}\n";
        echo "  - Mail type: {$gameGiftcode->mail}\n";
        echo "  - Max count: {$gameGiftcode->maxcount}\n";
        echo "  - Items: {$gameGiftcode->itemlist}\n";
    } else {
        echo "✗ Giftcode chưa được sync vào game server\n";
    }
    
    echo "\n";

    // Test 3: Tạo user và character test (giả lập)
    echo "3. Tạo test data...\n";
    
    // Tạo test account nếu chưa có
    $testAccount = DB::table('t_account')->where('UserName', 'testuser')->first();
    if (!$testAccount) {
        DB::table('t_account')->insert([
            'UserName' => 'testuser',
            'PassWord' => 'test123',
            'EMail' => '<EMAIL>',
            'RegTime' => now(),
            'LastTime' => now(),
            'OnlineTime' => 0,
            'TotalOnlineTime' => 0,
            'IsLock' => 0
        ]);
        $testUserId = DB::getPdo()->lastInsertId();
        echo "✓ Tạo test account: testuser (ID: $testUserId)\n";
    } else {
        $testUserId = $testAccount->ID;
        echo "✓ Sử dụng test account có sẵn: testuser (ID: $testUserId)\n";
    }
    
    // Tạo test character nếu chưa có
    $testCharacter = DB::connection('game_mysql')->table('t_roles')->where('rname', 'TestChar')->first();
    if (!$testCharacter) {
        DB::connection('game_mysql')->table('t_roles')->insert([
            'userid' => $testUserId,
            'rname' => 'TestChar',
            'occupation' => 1,
            'rlevel' => 1,
            'experience' => 0,
            'pkvalue' => 0,
            'money' => 0,
            'isdel' => 0,
            'regtime' => now(),
            'lasttime' => now()
        ]);
        $testCharacterId = DB::connection('game_mysql')->getPdo()->lastInsertId();
        echo "✓ Tạo test character: TestChar (ID: $testCharacterId)\n";
    } else {
        $testCharacterId = $testCharacter->rid;
        echo "✓ Sử dụng test character có sẵn: TestChar (ID: $testCharacterId)\n";
    }
    
    echo "\n";

    // Test 4: Sử dụng giftcode
    echo "4. Test sử dụng giftcode...\n";
    
    $useResult = $giftcodeService->useGiftcode('TESTUSE2025', $testUserId, $testCharacterId, 1);
    
    if ($useResult['success']) {
        echo "✓ Sử dụng giftcode thành công!\n";
        echo "  - Message: " . $useResult['message'] . "\n";
        if (isset($useResult['mail_id'])) {
            echo "  - Mail ID: " . $useResult['mail_id'] . "\n";
        }
    } else {
        echo "✗ Lỗi sử dụng giftcode: " . $useResult['message'] . "\n";
    }
    
    echo "\n";

    // Test 5: Kiểm tra log và record
    echo "5. Kiểm tra logs...\n";
    
    // Check website log
    $websiteLog = DB::table('t_giftcode_log')
        ->where('uid', $testUserId)
        ->where('giftcode', 'TESTUSE2025')
        ->first();
    
    if ($websiteLog) {
        echo "✓ Website log đã được tạo\n";
        echo "  - UID: {$websiteLog->uid}\n";
        echo "  - RID: {$websiteLog->rid}\n";
        echo "  - Zone: {$websiteLog->zoneid}\n";
    } else {
        echo "✗ Website log chưa được tạo\n";
    }
    
    // Check game server record
    $gameRecord = DB::connection('game_mysql')
        ->table('z_giftcoderecord')
        ->where('userid', 'testuser')
        ->where('code', 'TESTUSE2025')
        ->first();
    
    if ($gameRecord) {
        echo "✓ Game server record đã được tạo\n";
        echo "  - User: {$gameRecord->userid}\n";
        echo "  - Code: {$gameRecord->code}\n";
        echo "  - Zone: {$gameRecord->zoneid}\n";
    } else {
        echo "✗ Game server record chưa được tạo\n";
    }
    
    // Check game server giftcode count update
    $updatedGameGiftcode = DB::connection('game_mysql')
        ->table('z_giftcode')
        ->where('code', 'TESTUSE2025')
        ->first();
    
    if ($updatedGameGiftcode && $updatedGameGiftcode->count > 0) {
        echo "✓ Game server giftcode count đã được cập nhật: {$updatedGameGiftcode->count}\n";
    } else {
        echo "✗ Game server giftcode count chưa được cập nhật\n";
    }
    
    echo "\n";

    // Test 6: Thử sử dụng lại giftcode (should fail)
    echo "6. Test sử dụng lại giftcode...\n";
    
    $useAgainResult = $giftcodeService->useGiftcode('TESTUSE2025', $testUserId, $testCharacterId, 1);
    
    if (!$useAgainResult['success']) {
        echo "✓ Không thể sử dụng lại giftcode (đúng như mong đợi)\n";
        echo "  - Message: " . $useAgainResult['message'] . "\n";
    } else {
        echo "✗ Có thể sử dụng lại giftcode (lỗi logic)\n";
    }
    
    echo "\n=== Test hoàn thành ===\n";

} catch (Exception $e) {
    echo "✗ Lỗi: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
