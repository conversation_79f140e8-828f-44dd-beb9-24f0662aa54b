<?php

use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});

// Old UserCP route removed - functionality moved to new User namespace controllers

// Admin API Routes
Route::prefix('admin')->group(function () {
    // Authentication
    Route::post('/login', [App\Http\Controllers\Api\AdminAuthController::class, 'login']);
    Route::post('/logout', [App\Http\Controllers\Api\AdminAuthController::class, 'logout'])->middleware('auth:sanctum');
    Route::get('/user', [App\Http\Controllers\Api\AdminAuthController::class, 'user'])->middleware('auth:sanctum');

    // Protected admin routes
    Route::middleware(['auth:sanctum', 'admin'])->group(function () {
        // Dashboard
        Route::get('/dashboard/stats', [App\Http\Controllers\Api\DashboardController::class, 'stats']);

        // Account Management
        Route::get('/accounts/search', [App\Http\Controllers\Api\AccountController::class, 'search']);
        Route::get('/accounts/{id}', [App\Http\Controllers\Api\AccountController::class, 'show']);
        Route::put('/accounts/{id}', [App\Http\Controllers\Api\AccountController::class, 'update']);
        Route::post('/accounts/{id}/ban', [App\Http\Controllers\Api\AccountController::class, 'ban']);
        Route::post('/accounts/{id}/unban', [App\Http\Controllers\Api\AccountController::class, 'unban']);
        Route::post('/accounts/{id}/add-coin', [App\Http\Controllers\Api\AccountController::class, 'addCoin']);

        // Character Management
        Route::get('/characters/search', [App\Http\Controllers\Api\CharacterController::class, 'search']);
        Route::get('/characters/{id}', [App\Http\Controllers\Api\CharacterController::class, 'show']);
        Route::post('/characters/{id}/ban', [App\Http\Controllers\Api\CharacterController::class, 'ban']);
        Route::post('/characters/{id}/unban', [App\Http\Controllers\Api\CharacterController::class, 'unban']);
        Route::delete('/characters/{id}', [App\Http\Controllers\Api\CharacterController::class, 'destroy']);

        // Coin Recharge
        Route::get('/recharge/history', [App\Http\Controllers\Api\AdminRechargeController::class, 'index']);
        Route::post('/recharge/manual', [App\Http\Controllers\Api\AdminRechargeController::class, 'store']);
        Route::get('/recharge/statistics', [App\Http\Controllers\Api\AdminRechargeController::class, 'statistics']);
        Route::get('/recharge/{id}', [App\Http\Controllers\Api\AdminRechargeController::class, 'show']);
        Route::put('/recharge/{id}', [App\Http\Controllers\Api\AdminRechargeController::class, 'update']);
        Route::post('/recharge/{id}/approve', [App\Http\Controllers\Api\AdminRechargeController::class, 'approve']);
        Route::post('/recharge/{id}/reject', [App\Http\Controllers\Api\AdminRechargeController::class, 'reject']);

        // Battle Pass
        Route::get('/battle-pass/seasons', [App\Http\Controllers\Api\BattlePassController::class, 'index']);
        Route::post('/battle-pass/seasons', [App\Http\Controllers\Api\BattlePassController::class, 'store']);
        Route::get('/battle-pass/seasons/{id}', [App\Http\Controllers\Api\BattlePassController::class, 'show']);
        Route::put('/battle-pass/seasons/{id}', [App\Http\Controllers\Api\BattlePassController::class, 'update']);
        Route::delete('/battle-pass/seasons/{id}', [App\Http\Controllers\Api\BattlePassController::class, 'destroy']);
        Route::post('/battle-pass/add-exp', [App\Http\Controllers\Api\BattlePassController::class, 'addExp']);

        // IP Management
        Route::get('/ip-management', [App\Http\Controllers\Api\IPController::class, 'index']);
        Route::post('/ip-management', [App\Http\Controllers\Api\IPController::class, 'store']);
        Route::get('/ip-management/dashboard', [App\Http\Controllers\Api\IPController::class, 'dashboard']);
        Route::get('/ip-management/lookup', [App\Http\Controllers\Api\IPController::class, 'lookup']);
        Route::get('/ip-management/{id}', [App\Http\Controllers\Api\IPController::class, 'show']);
        Route::put('/ip-management/{id}', [App\Http\Controllers\Api\IPController::class, 'update']);
        Route::delete('/ip-management/{id}', [App\Http\Controllers\Api\IPController::class, 'destroy']);
        Route::post('/ip-management/{id}/unban', [App\Http\Controllers\Api\IPController::class, 'unban']);

        // Giftcode Management
        Route::get('/giftcodes', [App\Http\Controllers\Api\GiftcodeController::class, 'index']);
        Route::post('/giftcodes', [App\Http\Controllers\Api\GiftcodeController::class, 'store']);
        Route::get('/giftcodes/{id}', [App\Http\Controllers\Api\GiftcodeController::class, 'show']);
        Route::put('/giftcodes/{id}', [App\Http\Controllers\Api\GiftcodeController::class, 'update']);
        Route::delete('/giftcodes/{id}', [App\Http\Controllers\Api\GiftcodeController::class, 'destroy']);
        Route::get('/giftcodes/{id}/usage', [App\Http\Controllers\Api\GiftcodeController::class, 'usage']);
        Route::get('/giftcodes/{id}/export', [App\Http\Controllers\Api\GiftcodeController::class, 'export']);
    });
});

// User Giftcode Usage (with session support)
Route::middleware(['web'])->group(function () {
    Route::post('/giftcodes/use', [App\Http\Controllers\Api\GiftcodeController::class, 'use']);
    Route::get('/giftcodes/available', [App\Http\Controllers\Api\GiftcodeController::class, 'getAvailableGiftcodes']);
    Route::get('/user/characters', [App\Http\Controllers\Api\GiftcodeController::class, 'getUserCharacters']);
});
